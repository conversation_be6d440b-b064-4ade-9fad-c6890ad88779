import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>utt<PERSON>, PrimaryButton, mergeStyleSets, LayerHost, PivotItem, MessageBarType, Dialog, DialogFooter, DialogType, Spinner, SpinnerSize, IDropdownOption } from '@fluentui/react';
import { L } from '../../../lib/abpUtility';
import { KeysToIdMapper, ProductDto } from '../../../services/product/productDto';
import {additionalTheme, myTheme} from '../../../styles/theme';
import { GenericContentView, IGenericContentViewProps } from '../../BaseComponents/genericContentView';
import { defaultKeysToIdMapper, defaultProduct } from '../../../stores/productStore';
import Stores from '../../../stores/storeIdentifier';
import { inject } from 'mobx-react';
import policyCalculationService from '../../../services/policyCalculation/policyCalculationService';
import { StepsLayer } from './stepsLayer';
import { PolicyCalculationStep1 } from './policyCalculationStep1';
import { PolicyCalculationStep2 } from './policyCalculationStep2';
import { PolicyCalculationStep3 } from './policyCalculationStep3';
import { PolicyCalculationStep4 } from './policyCalculationStep4';
import { getGrandNodeLanguage } from '../../../utils/languageUtils';
import orderService from '../../../services/order/orderService';
import { defaultClient } from '../../../stores/clientStore';
import { buildRequestBody, getAttributeNameAndValue, getDataFromCalculationToEdit, getDataFromOrderToEdit, getUserFieldsFromProductAttributeMapping, getValueBasedOnClonedValue, mapAttributeKeyToId } from '../../../utils/policyCalculationUtils';
import policyApplicationService from '../../../services/policyApplication/policyApplicationService';
import { catchErrorMessage, filterBySome, isJsonString, peselDecode } from '../../../utils/utils';
import AppConfig from '../../../lib/appconfig';
import { OrderDto } from '../../../services/order/dto/orderDto';
import policyFinalizationService from '../../../services/policyFinalization/policyFinalizationService';
import insurancePolicyService from '../../../services/insurancePolicy/insurancePolicyService';
import { AutoCalculationOwnerType } from '../../../services/policyCalculation/autoCalculationOwnerEnums';
import { ClientDto } from '../../../services/client/dto/clientDto';
import { CalculationSectionType } from '../../../services/dto/calculationSectionTypeEnums';
import productService from '../../../services/product/productService';
import { CalculationDto } from '../../../services/calculation/dto/calculationDto';
import { PolicyType } from '../../../services/policy/policyTypeEnums';
import smsService from '../../../services/sms/smsService';
import { hourInMs, saveInStorage, validateLocalStorageKeyAndTimestamp } from '../../../utils/localStorageUtils';
import agreementsService from '../../../services/agreements/agreementsService';
import { AgreementDto } from '../../../services/agreements/dto/agreementDto';
import { CrudConsts } from '../../../stores/crudStoreBase';
import vehicleConfigService from '../../../services/vehicleConfig/vehicleConfigService';
import { IContentViewState } from '../../BaseComponents/IContentViewState';
import { validatePesel } from '../../../utils/inputUtils';
import moment, { Moment } from 'moment';
import clientAttachedFilesService from '../../../services/attachedFiles/clientAttachedFilesService';
import { OcTerminationCreationWay } from '../../../services/ocTermination/dto/ocTerminationCreationWayEnums';
import ocTerminationService from '../../../services/ocTermination/ocTerminationService';
import { ConfirmPaymentDto } from '../../../services/payment/dto/confirmPaymentDto';
import paymentService from '../../../services/payment/paymentService';

const classNames = mergeStyleSets({
    pageTitle: {
        color: additionalTheme.grey,
        fontSize: '26px',
        margin: "35px 0 65px 0"
    },
    fontBold: {
        fontWeight: '800',
    },
    layerHost: {
        marginBottom: '20px',
    },
    toolbar: {
        selectors: {
            '& .ms-Pivot': {
                display: 'none',
            }
        }
    },
    confrimButtonWrapper: {
        display: 'inline-flex',
        flexDirection: 'row',
        flexWrap: 'nowrap',
        justifyContent: 'flex-start',
        alignItems: 'center',
        width: 'fit-content',
    },
    loadSpinner: {
        display: 'inline-flex',
        selectors: {
            '& .ms-Spinner-label': {
                color: myTheme.palette.themePrimary,
            }
        }
    },
});

const pivotStyles = {
    root: {
        marginLeft: '-8px'
    },
    linkIsSelected: {
        color: myTheme.palette.red,
        selectors: {
            ':before': {
                height: '5px',
                backgroundColor: additionalTheme.darkerRed
            }
        }
    }
};

@inject(Stores.LanguageStore)
@inject(Stores.CountryStore)
@inject(Stores.SportDisciplineStore)
@inject(Stores.SportInsuranceCoverageStore)
@inject(Stores.ClientStore)
@inject(Stores.ClientLeasingStore)
@inject(Stores.VehicleStore)
@inject(Stores.ProductStore)
@inject(Stores.ProductAttributeStore)
@inject(Stores.OrderStore)
@inject(Stores.CalculationStore)
@inject(Stores.UserCrudStore)
@inject(Stores.TravelCountryStore)
export class PolicyCalculationContentView extends GenericContentView {
    private allProducts: ProductDto[] = [];
    private product: ProductDto | undefined = defaultProduct;
    private selectedProduct: string | number | undefined;
    private client: ClientDto | undefined = defaultClient;
    private tempSelectedClient: string = "";
    private tempSelectedVehicle: string = "";
    private selectedCustomer: string = "";
    private selectedClient: string = "";
    private selectedClientData: ClientDto | undefined = defaultClient;
    private calculations: any;
    private filteredCalculations: any;
    private selectedCalculation: any;
    private inputsTypeValuePairs: any = {};
    private hiddenInputsTypeValuePairs: any = {};
    private adjustInputsChangedManually: any = {};
    private inputsChangedManually: any = {};
    private inputsIdUserFieldsPairs: any = {};
    private tempInputsTypeValuePairsFromEdit: any = {};
    private clientTypeValuePairs: any = {};
    private gnLanguage: any = {};
    private step: number = 1;
    private prevProductId: string | undefined = "";
    private clientDataFilled: boolean = false;
    // private showConfirmationDialog: boolean = false;
    // private dialogConfirmed: boolean = false;
    private showPrevStepConfirmationDialog: boolean = false;
    private goBackToStep2Confirmed: boolean = false;
    private orderCreated: boolean = false;
    private policyFinalized: boolean = false;
    private orderSent: boolean = false;
    private inspectionLinkSent: boolean = false;
    private uwAcceptanceCode: string = '';
    private summaryMessageBoxData: any = {
        address: {
            type: MessageBarType.error,
            text: "",
            hide: true
        },
        createOrder: {
            type: MessageBarType.error,
            text: "",
            hide: true
        },
        policyApplication: {
            type: MessageBarType.error,
            text: "",
            hide: true
        },
        sendSms: {
            type: MessageBarType.error,
            text: "",
            hide: true
        },
        policyCalculation: {
            type: MessageBarType.error,
            text: "",
            hide: true
        },
        policyFinalization: {
            type: MessageBarType.error,
            text: "",
            hide: true
        },
        ocTerminationGenerated: {
            type: MessageBarType.error,
            text: "",
            hide: true
        },
        sendPolicyMail: {
            type: MessageBarType.error,
            text: "",
            hide: true
        },
        documentsGeneratedAndDownloaded: {
            type: MessageBarType.error,
            text: "",
            hide: true
        }
    };
    private customInputsData: any = {};
    private editedOrder: any = {};
    private isEditMode: boolean = false;
    private isFastCalculation: boolean = false;
    private _isMounted: boolean = false;
    private templateInputsForCalculationAdjust: any = {};
    private templateInputsForCalculationAdjustList: JSX.Element[] = [];
    private getCalculationsRawResponse: any = {};
    private autoCalculationOwner: string = "";
    private inputErrors: number = 0;
    private blockNextStepButton: boolean = false;
    private savedProductAttributeMappings: any = {
        productAttributeMappingsToWatch: {} as any,
        productAttributeMappingsToChange: {} as any,
    };
    private insurancePolicyResponse: any = {};
    private _returnedCreatedOrder: any;
    private _returnedAbpPolicyId: number = 0;
    private keysToIdMapper: KeysToIdMapper = defaultKeysToIdMapper;
    private allUserFields: any[] = [];
    private gotUserFieldsForSelectedProduct: string = '';
    private lastGetCalculationsPayload: any = '';
    private lastGetCalculationPayload: any = {};
    private inputValuePairsStringified: string = '';
    private savedMappedIdsForLaterUse: any = {};
    private initDataCatchedForCustomInputs: any = {
        insurerIsTravelParticipant: false as boolean,
        insurerIsCancelTravelParticipant: false as boolean,
    };
    private productAttributes: any = {};
    private setDelayedInputNewValue: any = {};
    private prevTravelTypeOfCalculation: string = '';
    private selectedOfferAgreements: AgreementDto[] = [];
    private preSettedApkData: any[] = [];
    private summaryAgreements: AgreementDto[] = [];
    private vehicleHasCoowner: boolean = false;
    private insurerIsInsured: boolean = false;
    private disabledInputsIds: string[] = [];
    private disabledInputOptionsIds: string[] = [];
    private customPresettedInputOptions: any = {};
    private childTabSwitch = null;
    private formChildTabSwitch = (func: any) => {
        this.childTabSwitch = func;
    };
    private tabsSelectedKey: any;
    private tabs: any[] = [
        // { key: UserField.Key, name: displayName, items: [{value, order}, {value, order}] }
    ];
    private eurotaxInfoexpertFormData: any = {
        type: '',
        brand: '',
        year: '',
        fuelType: '',
        engineCapacity: '',
        model: '',
        enginePower: '',
        eurotax: '',
        infoExpert: '',
        vin: '',
    };
    private step2VehicleDataIsSetCounter: number = 0;
    private forcedLastChangedSection: string | undefined = undefined;
    private inputErrorsText: any = {};
    private savedTemplateInputsForTable: any = {};
    private initDataForTravelProductDownloaded: boolean = false;
    
    async componentDidMount() {
        this._isMounted = true;

        this.toggleAsyncActionFlag(true);
        
        if(validateLocalStorageKeyAndTimestamp('policyCalculationKeysToIdMapper', hourInMs * 12)) {
            this.keysToIdMapper = JSON.parse(localStorage.getItem('policyCalculationKeysToIdMapper')!).data;
        } else {
            this.changeLoadSpinnerLabel(L('Downloading product keys for ID mapper.'));
            this.keysToIdMapper = await productService.getProductKeysToIdMapper();
            saveInStorage('policyCalculationKeysToIdMapper', {data: this.keysToIdMapper, timestamp: new Date().getTime()});
        }

        this.savedMappedIdsForLaterUse = {
            insurerIsTravelParticipant: this.mapKeyToId("mapAttributeNameToId", "insurerIsTravelParticipant"),
            insurerIsCancelTravelParticipant: this.mapKeyToId("mapAttributeNameToId", "insurerIsCancelTravelParticipant"),
            travelCountryList: this.mapKeyToId("mapAttributeNameToId", "travelCountryList"),
            insurerIsTravelParticipantOption: this.mapKeyToId("mapAttributeValueToOptionId", "insurerIsTravelParticipant"),
            insurerIsCancelTravelParticipantOption: this.mapKeyToId("mapAttributeValueToOptionId", "insurerIsCancelTravelParticipant"),
            travelSportList: this.mapKeyToId("mapAttributeNameToId", "travelSportList"),
            travelTypeOfCalculation: this.mapKeyToId("mapAttributeNameToId", "travelTypeOfCalculation"),
            travelTypeOfCalculationLimitedOption: this.mapKeyToId("mapAttributeValueToOptionId", "TYPE_OF_CALCULATION_LIMITED"),
            travelTypeOfCalculationFullOption: this.mapKeyToId("mapAttributeValueToOptionId", "TYPE_OF_CALCULATION_FULL"),
            cancelTravelTypeOfCalculation: this.mapKeyToId("mapAttributeNameToId", "cancelTravelTypeOfCalculation"),
            autoProductionYear: this.mapKeyToId("mapAttributeNameToId", "productionYear"),
            autoFirstRegistrationDate: this.mapKeyToId("mapAttributeNameToId", "firstRegistrationDate"),
            travelersTableId: this.mapKeyToId("mapAttributeNameToId", "travelersTable"),
            travelersTableFullDataId: this.mapKeyToId("mapAttributeNameToId", "travelersTableFullData"),
            travelStartDateId: this.mapKeyToId("mapAttributeNameToId", "travelStartDate"),
            travelEndDateId: this.mapKeyToId("mapAttributeNameToId", "travelEndDate"),
            cancelTravelersTableId: this.mapKeyToId("mapAttributeNameToId", "cancelTravelersTable"),
            autoMaritalStatus: this.mapKeyToId("mapAttributeNameToId", "maritalStatus"),
            autoYearOfPurchaseOfTheVehicle: this.mapKeyToId("mapAttributeNameToId", "yearOfPurchaseOfTheVehicle"),
            autoDrivingLicenceIssueYear: this.mapKeyToId("mapAttributeNameToId", "drivingLicenceIssueYear"),
            autoYearOfBirthOldestChild: this.mapKeyToId("mapAttributeNameToId", "vehicleYearOfBirthOldestChild"),
            autoVin: this.mapKeyToId("mapAttributeNameToId", "vin"),
            childNnwChildPesel: this.mapKeyToId("mapAttributeNameToId", "childNnwChildPesel"),
            cancelTravelStartDate: this.mapKeyToId("mapAttributeNameToId", "cancelTravelStartDate"),
            cancelTravelEndDate: this.mapKeyToId("mapAttributeNameToId", "cancelTravelEndDate"),
            cancelTravelPurchaseDate: this.mapKeyToId("mapAttributeNameToId", "cancelTravelPurchaseDate"),
            cancelTravelInsuranceSum: this.mapKeyToId("mapAttributeNameToId", "cancelTravelInsuranceSum"),
            cancelNumberOfPeopleTraveling: this.mapKeyToId("mapAttributeNameToId", "cancelNumberOfPeopleTraveling"),
            autoRegistrationNumber: this.mapKeyToId("mapAttributeNameToId", "registrationNumber"),
            autoStartDateId: this.mapKeyToId("mapAttributeNameToId", "autoStartDate"),
            nnwdInsurerIsInsured: this.mapKeyToId("mapAttributeNameToId", "nnwdInsurerIsInsured"),
            nnwdInsuredIsInsurerOption: this.mapKeyToId("mapAttributeValueToOptionId", "NNWD_INSURED_IS_INSURER"),
            autoMileage: this.mapKeyToId("mapAttributeNameToId", "mileage"),
            nnwdStartDateId: this.mapKeyToId("mapAttributeNameToId", "nnwdStartDate"),
        };

        if(validateLocalStorageKeyAndTimestamp('policyCalculationGnLanguage', hourInMs * 12)) {
            this.gnLanguage = JSON.parse(localStorage.getItem('policyCalculationGnLanguage')!).data;
        } else {
            this.changeLoadSpinnerLabel(L('Downloading languages.'));
            await this.props.languageStore?.getAll(this.props.languageStore?.defaultRequest);
            this.gnLanguage = getGrandNodeLanguage(this.props.languageStore?.dataSet);
            saveInStorage('policyCalculationGnLanguage', {data: this.gnLanguage, timestamp: new Date().getTime()});
        }

        if(validateLocalStorageKeyAndTimestamp('policyCalculationAllProducts', hourInMs * 12)) {
            this.allProducts = JSON.parse(localStorage.getItem('policyCalculationAllProducts')!).data;
        } else {
            this.changeLoadSpinnerLabel(L('Downloading products.'));
            await productService.getAllForDropdown().then((result: any) => {
                if(!!result && Array.isArray(result)) {
                    this.allProducts = result;
                    saveInStorage('policyCalculationAllProducts', {data: result, timestamp: new Date().getTime()});
                }
            });
        }
        
        if(!this.props.countryStore?.dataSet || (this.props.countryStore.dataSet && this.props.countryStore.dataSet.totalCount <= 0)) {
            this.changeLoadSpinnerLabel(L('Downloading countries.'));
            await this.props.countryStore!.getAll(this.props.countryStore!.defaultRequest);
        }

        if(this.props.policyCalculation?.payloadType === "customer" && this.props.policyCalculation?.payloadId && this.props.policyCalculation?.payloadId?.length > 0) {
            this.changeLoadSpinnerLabel(L('Downloading client data.'));
            this.client = await this.props.clientStore?.get({ id: this.props.policyCalculation?.payloadId } as ClientDto);
            this.selectedCustomer = this.props.clientStore?.model.customerId ? this.props.clientStore?.model?.customerId : "";
            this.selectedClient = this.props.policyCalculation?.payloadId;
            this.tempSelectedClient = this.props.policyCalculation?.payloadId;
            this.selectedClientData = this.client;
            this.clientDataFilled = true;
        }
        
        if(this.props.policyCalculation?.payloadType === "product" && this.props.policyCalculation?.payloadId && this.props.policyCalculation?.payloadId?.length > 0) {
            this.selectedProduct = this.props.policyCalculation?.payloadId;
        }

        if(this.props.policyCalculation?.payloadType === "order" && this.props.policyCalculation?.payloadId && this.props.policyCalculation?.payloadId?.length > 0) {
            this.changeLoadSpinnerLabel(L('Downloading order data.'));
            await this.props.orderStore?.get({ id: this.props.policyCalculation?.payloadId } as OrderDto);
            this.editedOrder = this.props.orderStore?.model;
            this.isEditMode = true;

            this.selectedProduct = this.editedOrder.OrderItems[0].Id;
            let productId = this.selectedProduct?.toString() ?? "";
            let dataFromOrder = getDataFromOrderToEdit(this.editedOrder.OrderItems[0].Attributes, productId, this.keysToIdMapper);
            
            this.selectedCustomer = this.editedOrder.CustomerId;

            if(dataFromOrder && dataFromOrder.dataObjFiltered) {
                dataFromOrder.dataObjFiltered.forEach((dataObj: any) => {
                    if(!!dataObj.valueId) {
                        this.tempInputsTypeValuePairsFromEdit[dataObj.productAttributeId] = dataObj.valueId;
                    } else {
                        this.tempInputsTypeValuePairsFromEdit[dataObj.productAttributeId] = dataObj.value;
                    }
                });
            } else if(dataFromOrder && dataFromOrder.apiCall && dataFromOrder.apiCall.source) {
                for(let key in dataFromOrder.apiCall.source) {
                    if(dataFromOrder.apiCall.source.hasOwnProperty(key)) {
                        this.tempInputsTypeValuePairsFromEdit[key] = dataFromOrder.apiCall.source[key];
                    }
                };
            }
        }

        if(this.props.policyCalculation?.payloadType === "calculation" && this.props.policyCalculation?.payloadId && this.props.policyCalculation?.payloadId?.length > 0) {
            this.changeLoadSpinnerLabel(L('Downloading calculation data.'));
            await this.props.calculationStore?.get({ id: this.props.policyCalculation?.payloadId } as CalculationDto).catch((error: any) => {
                this.catchError(error, "other");
            });
            const editedCalculation = this.props.calculationStore?.model;
            this.isEditMode = true;

            if(!!editedCalculation) {
                let dataFromCalculation = getDataFromCalculationToEdit(editedCalculation?.payload ? editedCalculation?.payload : '');

                if(dataFromCalculation && Object.keys(dataFromCalculation).length > 0) {
                    if(dataFromCalculation.type === PolicyType.Vehicle) {
                        this.selectedProduct = this.mapKeyToId("mapProductNameToProductId", "ubezpieczenie-auta");
                    } else if(dataFromCalculation.type === PolicyType.Home) {
                        this.selectedProduct = this.mapKeyToId("mapProductNameToProductId", "ubezpieczenie-domu");
                    } else if(dataFromCalculation.type === PolicyType.Life) {
                        this.selectedProduct = this.mapKeyToId("mapProductNameToProductId", "ubezpieczenie-na-życie");
                    } else if(dataFromCalculation.type === PolicyType.Travel) {
                        this.selectedProduct = this.mapKeyToId("mapProductNameToProductId", "podróż");
                    } else if(dataFromCalculation.type === PolicyType.Children) {
                        this.selectedProduct = this.mapKeyToId("mapProductNameToProductId", "nnw-dziecka");
                    } else if(dataFromCalculation.type === PolicyType.CancelTravel) {
                        this.selectedProduct = this.mapKeyToId("mapProductNameToProductId", "rezygnacja-z-podróży");
                    }
                    this.selectedCustomer = editedCalculation.clientId.toString();
                    this.selectedClient = editedCalculation.clientId.toString();

                    let euroTaxAndInfoExpertSetIterator: number = 0;
        
                    if(dataFromCalculation && dataFromCalculation.data) {
                        let concatedDataFromCalculation: any[] = [...dataFromCalculation.data];

                        if(dataFromCalculation.additionalData) {
                            let parsedAdditionalData = isJsonString(dataFromCalculation.additionalData) ? JSON.parse(dataFromCalculation.additionalData) : [];
                            if(parsedAdditionalData && parsedAdditionalData.length > 0) {
                                concatedDataFromCalculation = [...dataFromCalculation.data, ...parsedAdditionalData];
                            }
                        }

                        concatedDataFromCalculation.forEach((dataObj: any) => {
                            if(!!dataObj.valueId) {
                                this.tempInputsTypeValuePairsFromEdit[dataObj.productAttributeId] = isJsonString(dataObj.valueId) ? JSON.parse(dataObj.valueId) : dataObj.valueId;
                            } else {
                                this.tempInputsTypeValuePairsFromEdit[dataObj.productAttributeId] = dataObj.value;
                            }

                            if(!dataObj.is_for_apk) {
                                if(this.inputsIdUserFieldsPairs[dataObj.productAttributeId]) {
                                    this.inputsIdUserFieldsPairs[dataObj.productAttributeId] = [...this.inputsIdUserFieldsPairs[dataObj.productAttributeId], {"Key": "core_path", "Value": dataObj.core_path}, {"Key": "core_type", "Value": dataObj.core_type}];
                                } else {
                                    this.inputsIdUserFieldsPairs[dataObj.productAttributeId] = [{"Key": "core_path", "Value": dataObj.core_path}, {"Key": "core_type", "Value": dataObj.core_type}];
                                }
                            }

                            if(dataObj.core_path === 'VehicleInfo.EurotaxCarId') {
                                this.tempInputsTypeValuePairsFromEdit[this.mapKeyToId("mapAttributeNameToId", "findAVehicle")] = L('Success! Eurotax ID is now set.');
                                euroTaxAndInfoExpertSetIterator++;
                            } else if(dataObj.core_path === 'VehicleInfo.InfoExpertId') {
                                this.tempInputsTypeValuePairsFromEdit[this.mapKeyToId("mapAttributeNameToId", "findAVehicle")] = L("Success! Expert's information ID is now set.");
                                euroTaxAndInfoExpertSetIterator++;
                            }
    
                            if(euroTaxAndInfoExpertSetIterator === 2) {
                                this.tempInputsTypeValuePairsFromEdit[this.mapKeyToId("mapAttributeNameToId", "findAVehicle")] = L("Eurotax ID and Expert's information ID is now set.");
                            }
                        });
                    }
                }
            }
        }

        let isThisOriginProduct: boolean = (this.prevProductId && this.prevProductId?.length > 0 ? false : true);

        if(!!this.selectedProduct) {
            if(validateLocalStorageKeyAndTimestamp(`policyCalculationProduct${this.selectedProduct}`, hourInMs * 12, true)) {
                this.product = JSON.parse(sessionStorage.getItem(`policyCalculationProduct${this.selectedProduct}`)!).data;
                this.clearDataOnProductChange();

                if(isThisOriginProduct && this.isEditMode) {
                    this.inputsTypeValuePairs = {...this.inputsTypeValuePairs, ...this.tempInputsTypeValuePairsFromEdit};
                }
            } else {
                this.changeLoadSpinnerLabel(L('Downloading product data.'));
                await this.props.productStore?.getProductWithMappingsForProductId(this.selectedProduct.toString()).then((productResult: any) => {
                    this.product = productResult;
                    this.clearDataOnProductChange();
                    
                    if(isThisOriginProduct && this.isEditMode) {
                        this.inputsTypeValuePairs = {...this.inputsTypeValuePairs, ...this.tempInputsTypeValuePairsFromEdit};
                    }

                    saveInStorage(`policyCalculationProduct${this.selectedProduct}`, {data: this.product, timestamp: new Date().getTime()}, true);
                });
            }
        }

        if(validateLocalStorageKeyAndTimestamp('policyCalculationProductAttributes', hourInMs * 12)) {
            this.productAttributes = JSON.parse(localStorage.getItem('policyCalculationProductAttributes')!).data;
        } else {
            this.changeLoadSpinnerLabel(L('Downloading product attributes.'));
            await this.props.productAttributeStore?.getAll(this.props.productAttributeStore!.defaultRequest);
            this.productAttributes = this.props.productAttributeStore && this.props.productAttributeStore.dataSet && this.props.productAttributeStore.dataSet.items ? this.props.productAttributeStore.dataSet.items : {};
            saveInStorage('policyCalculationProductAttributes', {data: this.productAttributes, timestamp: new Date().getTime()});
        }

        this.changeLoadSpinnerLabel(null);
        
        this.isDataLoaded = true;
        this.toggleAsyncActionFlag(false);
        
        // ######## TODO - DEV - saved for maybe future feature - connected with testSetTable.tsx
        // if(checkIfExistInStorage('calculateAllSavedResult')) {
        //     let rawResult = loadFromStorage('calculateAllSavedResult');
        //     let parsedResult = JSON.parse(rawResult);

        //     if(!!parsedResult && parsedResult.PolicyCalculations) {
        //         this.step = 3;
        //         this.getCalculationsRawResponse = parsedResult;
        //         this.calculations = {};
        //         this.calculations['data'] = {};
        //         this.calculations['data']['error'] = null;
        //         this.calculations['data']['result'] = {};
        //         this.calculations['data']['result']['policyCalculations'] = [...parsedResult.PolicyCalculations];
        //         this.calculations['data']['result']['request'] = parsedResult.Request;

        //         this.toggleMessageBar('policyCalculation', MessageBarType.info, '', true);
        //         this.toggleMessageBar('other', MessageBarType.info, '', true);
        //     }
        //     removeFromStorage('calculateAllSavedResult');
        // }

        this.controlledForceUpdate('line 516');
    }

    async componentDidUpdate(prevProps: Readonly<IGenericContentViewProps>, prevState: Readonly<IContentViewState>, snapshot?: any): void {
        if(!!this.selectedProduct && this.selectedProduct !== this.gotUserFieldsForSelectedProduct) {
            this.gotUserFieldsForSelectedProduct = this.selectedProduct.toString();

            if(validateLocalStorageKeyAndTimestamp(`userFieldsForProductId${this.selectedProduct}`, hourInMs * 12, true)) {
                this.allUserFields = JSON.parse(sessionStorage.getItem(`userFieldsForProductId${this.selectedProduct}`)!).data;
                this.forceUpdate();
            } else {
                this.toggleAsyncActionFlag(true);
                this.changeLoadSpinnerLabel(L('Downloading user fields for selected product.'));

                await productService.getUserFieldsForProductId(this.selectedProduct.toString()).then((result: any) => {
                    if(!!result && Array.isArray(result)) {
                        this.allUserFields = result;
                        saveInStorage(`userFieldsForProductId${this.selectedProduct}`, {data: result, timestamp: new Date().getTime()}, true);
                    }
                });

                this.changeLoadSpinnerLabel(null);
                this.toggleAsyncActionFlag(false);
            }
        }

        if(this.step === 1 && this.selectedProduct === this.mapKeyToId("mapProductNameToProductId", "podróż") && this.initDataForTravelProductDownloaded === false) {
            this.initDataForTravelProductDownloaded = true;
            this.toggleAsyncActionFlag(true);

            if(!this.props.travelCountryStore?.dataSet || (this.props.travelCountryStore.dataSet && this.props.travelCountryStore.dataSet.totalCount <= 0)) {
                this.changeLoadSpinnerLabel(L('Downloading countries.'));
                await this.props.travelCountryStore!.getAll({...this.props.travelCountryStore!.defaultRequest, maxResultCount: CrudConsts.LAZY_LOAD_PAGE_SIZE, sorting: 'name+asc'});
            }

            if(!this.props.sportDisciplineStore?.dataSet || (this.props.sportDisciplineStore.dataSet && this.props.sportDisciplineStore.dataSet.totalCount <= 0)) {
                this.changeLoadSpinnerLabel(L('Downloading sport disciplines.'));
                await this.props.sportDisciplineStore!.getAll(this.props.sportDisciplineStore!.defaultRequest);
            }

            if(!this.props.sportInsuranceCoverageStore?.dataSet || (this.props.sportInsuranceCoverageStore.dataSet && this.props.sportInsuranceCoverageStore.dataSet.totalCount <= 0)) {
                this.changeLoadSpinnerLabel(L('Downloading sport insurance coverage.'));
                await this.props.sportInsuranceCoverageStore!.getAll(this.props.sportInsuranceCoverageStore!.defaultRequest);
            }

            this.changeLoadSpinnerLabel(null);
            this.toggleAsyncActionFlag(false);
        }

        if(this.step2VehicleDataIsSetCounter < 6 && this.selectedProduct === this.mapKeyToId("mapProductNameToProductId", "ubezpieczenie-auta") && this.step === 2 && 
            this.loadSpinnerCustomLabel === null && this.isDataLoaded === true && this.clientDataFilled === true
        ) {
            if(!!this.client?.dateOfBirth || !!this.client?.pesel) {
                let yearOfBirth: number = 0;

                if(!!this.client.dateOfBirth) {
                    yearOfBirth = new Date(this.client.dateOfBirth).getFullYear();
                } else {
                    yearOfBirth = peselDecode(this.client.pesel).date.getFullYear();
                }

                const tempDropdownOptions: IDropdownOption[] = [];
                
                for(let i: number = yearOfBirth + 16; i <= new Date().getFullYear(); i++) {
                    tempDropdownOptions.push({
                        key: i, 
                        text: i.toString()
                    });
                }
                
                this.customPresettedInputOptions[this.savedMappedIdsForLaterUse.autoDrivingLicenceIssueYear] = tempDropdownOptions as IDropdownOption[];
            }

            if(this.client && !!this.client.yearOfBirthOldestChild && !this.inputsTypeValuePairs[this.savedMappedIdsForLaterUse.autoYearOfBirthOldestChild]) {
                this.inputsTypeValuePairs[this.savedMappedIdsForLaterUse.autoYearOfBirthOldestChild] = this.client.yearOfBirthOldestChild.toString();
            }
            
            if(this.client && !!this.client.drivingLicenceIssueYear && !this.inputsTypeValuePairs[this.savedMappedIdsForLaterUse.autoDrivingLicenceIssueYear]) {
                this.inputsTypeValuePairs[this.savedMappedIdsForLaterUse.autoDrivingLicenceIssueYear] = this.client.drivingLicenceIssueYear;
            }

            this.step2VehicleDataIsSetCounter = this.step2VehicleDataIsSetCounter + 1;
        }

        if(this.isDataLoaded === false && this.productAttributes && Object.keys(this.productAttributes).length > 0) {
            this.isDataLoaded = true;
        }
    }

    componentWillUnmount() {
        this._isMounted = false;

        this.inputsTypeValuePairs = {};
        this.inputsIdUserFieldsPairs = {};
        this.customInputsData = {};
        this.selectedCalculation = undefined;
        this.selectedCustomer = "";
        this.selectedClient = "";
        this.selectedProduct = undefined;
        this.prevProductId = undefined;
    }

    mapKeyToId(mapType: string, key: string): string {
        switch(mapType) {
            case "mapAttributeNameToId":
                return this.mapAttributeNameToId(key);
            case "mapAttributeValueToOptionId":
                return this.mapAttributeValueToOptionId(key);
            case "mapProductNameToProductId":
                return this.mapProductNameToProductId(key);
            case "mapProductIdToType":
                return this.mapProductIdToType(key);
            default:
                return "";    
        }
    }

    private mapAttributeNameToId(name: string) {
        if(this.keysToIdMapper !== defaultKeysToIdMapper) {
            let findedId = this.keysToIdMapper.attributeNameToIdList
                                                    .filter(x => x.key === name)
                                                    .map(x => x.id);

            if(findedId[0] !== undefined) {
                return findedId[0];
            }
        }
        return "";
    }

    private mapAttributeValueToOptionId(name: string) {
        if (this.keysToIdMapper !== defaultKeysToIdMapper) {
            let findedId = this.keysToIdMapper.attributeValueToOptionIdList
                                                    .filter(x => x.key === name)
                                                    .map(x => x.id);
            if(findedId[0] !== undefined){
                return findedId[0];
            }
        }
        return "";
    }

    private mapProductNameToProductId(name: string) {
        if (this.keysToIdMapper !== defaultKeysToIdMapper) {
            let findedId = this.keysToIdMapper.productKeyToProductIdList
                                                    .filter(x => x.key === name)
                                                    .map(x => x.id);
            if(findedId[0] !== undefined){
                return findedId[0];
            }
        }
        return "";
    }

    private mapProductIdToType(id: string) {
        if (this.keysToIdMapper !== defaultKeysToIdMapper) {
            let findedId = this.keysToIdMapper.productIdToTypeList
                                                    .filter(x => x.id === id)
                                                    .map(x => x.key);
            if(findedId[0] !== undefined){
                return findedId[0];
            }
        }
        return "";
    }

    private getProductTypeByProduct(product: ProductDto | undefined) {
        return this.mapProductIdToType(this.getProductId(product));
    }

    getMapNameByProduct(product: ProductDto | undefined) {
        return this.mapProductIdToMapName(this.getProductId(product));
    }

    private mapProductIdToMapName(id: string) {
        if(this.keysToIdMapper !== defaultKeysToIdMapper) {
            let findedId = this.keysToIdMapper.productIdToMapNameList
                                                    .filter(x => x.id === id)
                                                    .map(x => x.key);
            if(findedId[0] !== undefined) {
                return findedId[0];
            }
        }
        return "";
    }

    private getProductId(product: ProductDto | undefined) {
        if (product !== null && product !== undefined){
            return product?.Id;
        }
        return "";
    }

    private controlledForceUpdate(whereCalled?: string) {
        if(this._isMounted === true) {
            this.forceUpdate();
        }
    }

    private changeLoadSpinnerLabel(newValue: string | null) {
        this.loadSpinnerCustomLabel = newValue;
        this.controlledForceUpdate('line 535');
    }

    private clearDataOnProductChange() {
        if(this.prevProductId !== this.product?.Id) {
            this.inputsTypeValuePairs = {};
            this.customInputsData = {};
            this.selectedCalculation = undefined;
            this.prevProductId = this.product?.Id;
        }
    }

    private loopInputsTypeValuePairs() {
        for(let key in this.inputsTypeValuePairs) {
            if(this.inputsTypeValuePairs.hasOwnProperty(key)) {
                this.catchDataForCustomInputs(key, this.inputsTypeValuePairs[key]);
            }
        }

        this.controlledForceUpdate('line 241');
    }

    private catchDataForCustomerInputs(id: string, value: string, userFields: any) {
        if(!!userFields) {
            userFields.some((UserField: any) => {
                if(UserField.Key === "key") {
                    let splittedKey = UserField.Value.split('.');
                    
                    if(['Insurer', 'VehicleOwner', 'VehicleCoOwner', 'VehicleUser'].includes(splittedKey[1])) {
                        let newSectionKey: string = "";
                        switch(splittedKey[1]) {
                            case 'Insurer':
                                newSectionKey = CalculationSectionType.Insurer;
                                break;
                            case 'VehicleOwner':
                                newSectionKey = CalculationSectionType.Owner;
                                break;
                            case 'VehicleCoOwner':
                                newSectionKey = CalculationSectionType.CoOwner;
                                break;
                            case 'VehicleUser':
                                newSectionKey = CalculationSectionType.User;
                                break;
                        }
    
                        if(!this.clientTypeValuePairs[newSectionKey]) {
                            this.clientTypeValuePairs[newSectionKey] = {};
                        }
                        
                        this.clientTypeValuePairs[newSectionKey][UserField.Value] = value;
                    }
    
                    return true;
                }
    
                return false;
            });
        }
    }

    private saveInputsForCalculationAdjust(inputs: any) {
        this.templateInputsForCalculationAdjust = inputs;

        for(let key in inputs) {
            if(inputs.hasOwnProperty(key)) {
                if(inputs[key].attr && inputs[key].attr.Id && inputs[key].userFields) {
                    this.inputsIdUserFieldsPairs[inputs[key].attr.Id] = inputs[key].userFields;
                }
            }
        }

        this.controlledForceUpdate('line 291');
    }

    private catchDataForCustomInputs(id: string, value: string | number) {
        let caseFoundFlag: boolean = false;

        switch(id) {
            case 'vehicleBrand':
                this.customInputsData['vehicleBrand'] = value;
                delete this.customInputsData['vehicleModel'];
                caseFoundFlag = true;
            break;
                
            case 'vehicleModel':
                this.customInputsData['vehicleModel'] = value;
                caseFoundFlag = true;
            break;
    
            // eurotax
            case 'vehicleTypeId':
                this.customInputsData['vehicleTypeId'] = value;
                delete this.customInputsData['vehicleBrandId'];
                delete this.customInputsData['productionYear'];
                delete this.customInputsData['fuelType'];
                delete this.customInputsData['vehicleModelId'];
                delete this.customInputsData['vehicleConfigurationId'];
                delete this.customInputsData['vehicleConfigurationEurotaxId'];
                delete this.customInputsData['vehicleConfigurationInfoExpertId'];
                delete this.customInputsData['enginePower'];
                delete this.customInputsData['engineCapacity'];
                caseFoundFlag = true;
            break;
    
            case 'vehicleBrandId':
                if(!!this.customInputsData['vehicleBrandId']) {
                    delete this.customInputsData['productionYear'];
                }
    
                this.customInputsData['vehicleBrandId'] = value;
                delete this.customInputsData['productionYear'];
                delete this.customInputsData['fuelType'];
                delete this.customInputsData['vehicleModelId'];
                delete this.customInputsData['vehicleConfigurationId'];
                delete this.customInputsData['vehicleConfigurationEurotaxId'];
                delete this.customInputsData['vehicleConfigurationInfoExpertId'];
                delete this.customInputsData['enginePower'];
                delete this.customInputsData['engineCapacity'];
                caseFoundFlag = true;
            break;
    
            case 'productionYear':
                this.customInputsData['productionYear'] = value;
                this.inputsTypeValuePairs[this.savedMappedIdsForLaterUse.autoProductionYear] = typeof value === 'string' && !!value ? parseInt(value) : value;
                delete this.customInputsData['fuelType'];
                delete this.customInputsData['vehicleModelId'];
                delete this.customInputsData['vehicleConfigurationId'];
                delete this.customInputsData['vehicleConfigurationEurotaxId'];
                delete this.customInputsData['vehicleConfigurationInfoExpertId'];
                delete this.customInputsData['enginePower'];
                delete this.customInputsData['engineCapacity'];
                
                caseFoundFlag = true;
            break;

            case 'fuelType':
                this.customInputsData['fuelType'] = value;
                delete this.customInputsData['vehicleConfigurationId'];
                delete this.customInputsData['vehicleConfigurationEurotaxId'];
                delete this.customInputsData['vehicleConfigurationInfoExpertId'];
                delete this.customInputsData['vehicleModelId'];
                delete this.customInputsData['engineCapacity'];
                delete this.customInputsData['enginePower'];
                caseFoundFlag = true;
            break;
    
            case 'engineCapacity':
                this.customInputsData['engineCapacity'] = value;
                delete this.customInputsData['vehicleConfigurationId'];
                delete this.customInputsData['vehicleConfigurationEurotaxId'];
                delete this.customInputsData['vehicleConfigurationInfoExpertId'];
                delete this.customInputsData['vehicleModelId'];
                delete this.customInputsData['enginePower'];
                caseFoundFlag = true;
            break;

            case 'vehicleModelId':
                this.customInputsData['vehicleModelId'] = value;
                delete this.customInputsData['vehicleConfigurationId'];
                delete this.customInputsData['vehicleConfigurationEurotaxId'];
                delete this.customInputsData['vehicleConfigurationInfoExpertId'];
                delete this.customInputsData['enginePower'];
                caseFoundFlag = true;
            break;
    
            case 'enginePower':
                this.customInputsData['enginePower'] = value;
                delete this.customInputsData['vehicleConfigurationId'];
                delete this.customInputsData['vehicleConfigurationEurotaxId'];
                delete this.customInputsData['vehicleConfigurationInfoExpertId'];
                caseFoundFlag = true;
            break;
    
            case 'vehicleConfigurationId':
                this.customInputsData['vehicleConfigurationId'] = value;
                caseFoundFlag = true;
            break;
            
            case 'vehicleConfigurationEurotaxId':
                this.customInputsData['vehicleConfigurationEurotaxId'] = value;
                caseFoundFlag = true;
            break;

            case 'vehicleConfigurationInfoExpertId':
                this.customInputsData['vehicleConfigurationInfoExpertId'] = value;
                caseFoundFlag = true;
            break;

            case this.savedMappedIdsForLaterUse.autoMaritalStatus:
                if((this.client && !!this.client.id) && (!this.inputsTypeValuePairs[id] || this.inputsTypeValuePairs[id] === 'null')) {
                    if(!!this.client?.maritalStatus) {
                        this.inputsTypeValuePairs[id] = this.mapKeyToId("mapAttributeValueToOptionId", this.client.maritalStatus);
                    } else {
                        delete this.inputsTypeValuePairs[id];
                    }
                }
            break;

            case this.savedMappedIdsForLaterUse.travelTypeOfCalculation:
            case this.savedMappedIdsForLaterUse.insurerIsTravelParticipant:
                let valueIsFromCalculationTypeInput: boolean = value === this.savedMappedIdsForLaterUse.travelTypeOfCalculationLimitedOption || value === this.savedMappedIdsForLaterUse.travelTypeOfCalculationFullOption ? true : false;
                let isCalculationTypeFullData: boolean = false;
                if(value === this.savedMappedIdsForLaterUse.travelTypeOfCalculationFullOption || 
                    (!valueIsFromCalculationTypeInput && this.inputsTypeValuePairs[this.savedMappedIdsForLaterUse.travelTypeOfCalculation] === this.savedMappedIdsForLaterUse.travelTypeOfCalculationFullOption)
                ) {
                    isCalculationTypeFullData = true;
                }
                
                const travelerNumberId: string = isCalculationTypeFullData === false ? this.mapKeyToId("mapAttributeNameToId", "travelerNumber") : this.mapKeyToId("mapAttributeNameToId", "travelerNumberFullData");
                const travelerFirstNameId: string = isCalculationTypeFullData === false ? this.mapKeyToId("mapAttributeNameToId", "travelerFirstName") : this.mapKeyToId("mapAttributeNameToId", "travelerFirstNameFullData");
                const travelerSurnameId: string = isCalculationTypeFullData === false ? this.mapKeyToId("mapAttributeNameToId", "travelerSurname") : this.mapKeyToId("mapAttributeNameToId", "travelerSurnameFullData"); 
                const travelerDateOfBirthId: string = isCalculationTypeFullData === false ? this.mapKeyToId("mapAttributeNameToId", "travelerDateOfBirth") : this.mapKeyToId("mapAttributeNameToId", "travelerDateOfBirthFullData");
                const travelerCityFullDataId: string = isCalculationTypeFullData === true ? this.mapKeyToId("mapAttributeNameToId", "travelerCityFullData") : '';
                const travelerHouseNumberFullDataId: string = isCalculationTypeFullData === true ? this.mapKeyToId("mapAttributeNameToId", "travelerHouseNumberFullData") : '';
                const travelerPeselFullDataId: string = isCalculationTypeFullData === true ? this.mapKeyToId("mapAttributeNameToId", "travelerPeselFullData") : '';
                const travelerCountyFullDataId: string = isCalculationTypeFullData === true ? this.mapKeyToId("mapAttributeNameToId", "travelerCountyFullData") : '';
                const travelerCountryFullDataId: string = isCalculationTypeFullData === true ? this.mapKeyToId("mapAttributeNameToId", "travelerCountryFullData") : '';
                const travelerStreetFullDataId: string = isCalculationTypeFullData === true ? this.mapKeyToId("mapAttributeNameToId", "travelerStreetFullData") : '';
                const travelerPostCodeFullDataId: string = isCalculationTypeFullData === true ? this.mapKeyToId("mapAttributeNameToId", "travelerPostCodeFullData") : '';
                const travelerPhoneNumberId: string = isCalculationTypeFullData === true ? this.mapKeyToId("mapAttributeNameToId", "travelerPhoneNumberFullData") : '';

                const numberOfPeopleTravelingId: string = isCalculationTypeFullData === false ? this.mapKeyToId("mapAttributeNameToId", "numberOfPeopleTraveling") : this.mapKeyToId("mapAttributeNameToId", "numberOfPeopleTravelingFullData");

                const travelInsurerFirstNameId: string = this.mapKeyToId("mapAttributeNameToId", "travelInsurerFirstName");
                const travelInsurerSurnameId: string = this.mapKeyToId("mapAttributeNameToId", "travelInsurerSurname");
                const travelInsurerPeselId: string = this.mapKeyToId("mapAttributeNameToId", "travelInsurerPesel");
                const travelInsurerPostCodeId: string = isCalculationTypeFullData === true ? this.mapKeyToId("mapAttributeNameToId", "travelInsurerPostCode") : '';
                const travelInsurerCityId: string = isCalculationTypeFullData === true ? this.mapKeyToId("mapAttributeNameToId", "travelInsurerCity") : '';
                const travelInsurerHouseNumberId: string = isCalculationTypeFullData === true ? this.mapKeyToId("mapAttributeNameToId", "travelInsurerHouseNumber") : '';
                const travelInsurerMobilePhoneId: string = isCalculationTypeFullData === true ? this.mapKeyToId("mapAttributeNameToId", "travelInsurerMobilePhone") : '';
                const travelInsurerStreetId: string = isCalculationTypeFullData === true ? this.mapKeyToId("mapAttributeNameToId", "travelInsurerStreet") : '';
                const travelInsurerCountyId: string = isCalculationTypeFullData === true ? this.mapKeyToId("mapAttributeNameToId", "travelInsurerCounty") : '';
                const travelInsurerCountryId: string = isCalculationTypeFullData === true ? this.mapKeyToId("mapAttributeNameToId", "travelInsurerCountry") : '';

                let decodePesel: any = this.inputsTypeValuePairs[travelInsurerPeselId] ? peselDecode(this.inputsTypeValuePairs[travelInsurerPeselId].toString()) : undefined;

                const travelersTableIdToChange: string = isCalculationTypeFullData === false ? this.savedMappedIdsForLaterUse.travelersTableId : this.savedMappedIdsForLaterUse.travelersTableFullDataId;
                const travelersTableIdToRemove: string = isCalculationTypeFullData === true ? this.savedMappedIdsForLaterUse.travelersTableId : this.savedMappedIdsForLaterUse.travelersTableFullDataId;
                const travelersNumberOfPeopleTravelingToReset: string = isCalculationTypeFullData === true ? this.mapKeyToId("mapAttributeNameToId", "numberOfPeopleTraveling") : this.mapKeyToId("mapAttributeNameToId", "numberOfPeopleTravelingFullData");

                if(this.hiddenInputsTypeValuePairs[travelersTableIdToChange]) {
                    const newNumberOfPeopleTraveling = this.hiddenInputsTypeValuePairs[travelersTableIdToChange] ? 
                        (typeof this.hiddenInputsTypeValuePairs[travelersTableIdToChange] === 'object' ? Object.keys(this.hiddenInputsTypeValuePairs[travelersTableIdToChange]).length : 
                            (typeof this.hiddenInputsTypeValuePairs[travelersTableIdToChange] === 'string' && isJsonString(this.hiddenInputsTypeValuePairs[travelersTableIdToChange])) ?
                                Object.keys(JSON.parse(this.hiddenInputsTypeValuePairs[travelersTableIdToChange])).length : -1) : -1;

                    if(newNumberOfPeopleTraveling !== this.inputsTypeValuePairs[numberOfPeopleTravelingId]) {
                        this.inputsTypeValuePairs[numberOfPeopleTravelingId] = newNumberOfPeopleTraveling;
                    }

                    break;
                }

                if((value === this.savedMappedIdsForLaterUse.insurerIsTravelParticipantOption || 
                    (valueIsFromCalculationTypeInput && this.inputsTypeValuePairs[this.savedMappedIdsForLaterUse.insurerIsTravelParticipant] === this.savedMappedIdsForLaterUse.insurerIsTravelParticipantOption)) &&
                    (!this.inputsTypeValuePairs[this.savedMappedIdsForLaterUse.insurerIsTravelParticipant] || 
                        (this.inputsTypeValuePairs[this.savedMappedIdsForLaterUse.insurerIsTravelParticipant] !== this.savedMappedIdsForLaterUse.insurerIsTravelParticipantOption ||
                        (valueIsFromCalculationTypeInput && value !== this.prevTravelTypeOfCalculation)) ||
                        this.initDataCatchedForCustomInputs.insurerIsTravelParticipant === false)
                ) {
                    let addNewRow: boolean = false;

                    if(valueIsFromCalculationTypeInput) {
                        this.prevTravelTypeOfCalculation = typeof value === 'number' ? value.toString() : value;
                    }

                    if(isJsonString(this.inputsTypeValuePairs[travelersTableIdToChange])) {
                        const parsedTableData: any = JSON.parse(this.inputsTypeValuePairs[travelersTableIdToChange]);
                        let newObjToPush: any = undefined;

                        if(isCalculationTypeFullData) {
                            newObjToPush = {
                                // [travelerNumberId]: this.inputsTypeValuePairs[numberOfPeopleTravelingId],
                                [travelerFirstNameId]: this.inputsTypeValuePairs[travelInsurerFirstNameId],
                                [travelerSurnameId]: this.inputsTypeValuePairs[travelInsurerSurnameId],
                                [travelerPeselFullDataId]: this.inputsTypeValuePairs[travelInsurerPeselId],
                                [travelerCityFullDataId]: this.inputsTypeValuePairs[travelInsurerCityId],
                                [travelerStreetFullDataId]: this.inputsTypeValuePairs[travelInsurerStreetId],
                                [travelerHouseNumberFullDataId]: this.inputsTypeValuePairs[travelInsurerHouseNumberId],
                                [travelerPostCodeFullDataId]: this.inputsTypeValuePairs[travelInsurerPostCodeId],
                                [travelerCountyFullDataId]: this.inputsTypeValuePairs[travelInsurerCountyId],
                                [travelerCountryFullDataId]: this.inputsTypeValuePairs[travelInsurerCountryId],
                                [travelerPhoneNumberId]: this.inputsTypeValuePairs[travelInsurerMobilePhoneId],
                            };
                        } else {
                            newObjToPush = {
                                // [travelerNumberId]: this.inputsTypeValuePairs[numberOfPeopleTravelingId],
                                [travelerFirstNameId]: this.inputsTypeValuePairs[travelInsurerFirstNameId],
                                [travelerSurnameId]: this.inputsTypeValuePairs[travelInsurerSurnameId],
                                [travelerDateOfBirthId]: decodePesel && decodePesel.date ? decodePesel.date : '',
                            };
                        }

                        if(!this.inputsTypeValuePairs[travelersTableIdToChange].includes(JSON.stringify(newObjToPush).substring(1).slice(0, -15))) {
                            newObjToPush[travelerNumberId] = parseInt(this.inputsTypeValuePairs[numberOfPeopleTravelingId]) > 0 ? 
                                                                (parseInt(this.inputsTypeValuePairs[numberOfPeopleTravelingId]) + 1).toString() : "1";
                            parsedTableData[Object.keys(parsedTableData).length] = newObjToPush;
                            this.inputsTypeValuePairs[travelersTableIdToChange] = JSON.stringify(parsedTableData);
                            addNewRow = true;
                        }
                    } else {
                        addNewRow = true;

                        if(isCalculationTypeFullData) {
                            this.inputsTypeValuePairs[travelersTableIdToChange] = JSON.stringify({0: {
                                [travelerNumberId]: parseInt(this.inputsTypeValuePairs[numberOfPeopleTravelingId]) > 0 ? 
                                                        (parseInt(this.inputsTypeValuePairs[numberOfPeopleTravelingId]) + 1).toString() : "1",
                                [travelerFirstNameId]: this.inputsTypeValuePairs[travelInsurerFirstNameId],
                                [travelerSurnameId]: this.inputsTypeValuePairs[travelInsurerSurnameId],
                                [travelerPeselFullDataId]: this.inputsTypeValuePairs[travelInsurerPeselId],
                                [travelerCityFullDataId]: this.inputsTypeValuePairs[travelInsurerCityId],
                                [travelerStreetFullDataId]: this.inputsTypeValuePairs[travelInsurerStreetId],
                                [travelerHouseNumberFullDataId]: this.inputsTypeValuePairs[travelInsurerHouseNumberId],
                                [travelerPostCodeFullDataId]: this.inputsTypeValuePairs[travelInsurerPostCodeId],
                                [travelerCountyFullDataId]: this.inputsTypeValuePairs[travelInsurerCountyId],
                                [travelerCountryFullDataId]: this.inputsTypeValuePairs[travelInsurerCountryId],
                                [travelerPhoneNumberId]: this.inputsTypeValuePairs[travelInsurerMobilePhoneId],
                            }});
                        } else {
                            this.inputsTypeValuePairs[travelersTableIdToChange] = JSON.stringify({0: {
                                [travelerNumberId]: parseInt(this.inputsTypeValuePairs[numberOfPeopleTravelingId]) > 0 ? 
                                                        (parseInt(this.inputsTypeValuePairs[numberOfPeopleTravelingId]) + 1).toString() : "1",
                                [travelerFirstNameId]: this.inputsTypeValuePairs[travelInsurerFirstNameId],
                                [travelerSurnameId]: this.inputsTypeValuePairs[travelInsurerSurnameId],
                                [travelerDateOfBirthId]: decodePesel && decodePesel.date ? decodePesel.date : '',
                            }});
                        }
                    }

                    if(addNewRow === true) {
                        if(!this.inputsTypeValuePairs[numberOfPeopleTravelingId] || this.inputsTypeValuePairs[numberOfPeopleTravelingId].length === 0 || 
                            (typeof this.inputsTypeValuePairs[numberOfPeopleTravelingId] === 'string' && this.inputsTypeValuePairs[numberOfPeopleTravelingId] === '0') ||
                            (typeof this.inputsTypeValuePairs[numberOfPeopleTravelingId] === 'number' && this.inputsTypeValuePairs[numberOfPeopleTravelingId] === 0)
                        ) {
                            this.inputsTypeValuePairs[numberOfPeopleTravelingId] = '1';
                        } else if(typeof this.inputsTypeValuePairs[numberOfPeopleTravelingId] === 'string') {
                            this.inputsTypeValuePairs[numberOfPeopleTravelingId] = (parseInt(this.inputsTypeValuePairs[numberOfPeopleTravelingId]) + 1).toString();
                        } else if(typeof this.inputsTypeValuePairs[numberOfPeopleTravelingId] === 'number') {
                            this.inputsTypeValuePairs[numberOfPeopleTravelingId] = (this.inputsTypeValuePairs[numberOfPeopleTravelingId] + 1).toString();
                        }
                    }
                    
                    this.initDataCatchedForCustomInputs = {...this.initDataCatchedForCustomInputs, insurerIsTravelParticipant: true};
                }
                delete this.inputsTypeValuePairs[travelersTableIdToRemove];
                this.inputsTypeValuePairs[travelersNumberOfPeopleTravelingToReset] = '0';
            break;

            case this.savedMappedIdsForLaterUse.cancelTravelTypeOfCalculation:
            case this.savedMappedIdsForLaterUse.insurerIsCancelTravelParticipant:
                let valueIsFromCancelTravelCalculationTypeInput: boolean = !!value ? true : false;
                let isCancelTravelCalculationTypeFullData: boolean = false;
                
                const cancelTravelerNumberId: string = isCancelTravelCalculationTypeFullData === false ? this.mapKeyToId("mapAttributeNameToId", "cancelTravelerNumber") : this.mapKeyToId("mapAttributeNameToId", "cancelTravelerNumber");
                const cancelTravelerFirstNameId: string = isCancelTravelCalculationTypeFullData === false ? this.mapKeyToId("mapAttributeNameToId", "cancelTravelerFirstName") : this.mapKeyToId("mapAttributeNameToId", "cancelTravelerFirstName");
                const cancelTravelerSurnameId: string = isCancelTravelCalculationTypeFullData === false ? this.mapKeyToId("mapAttributeNameToId", "cancelTravelerSurname") : this.mapKeyToId("mapAttributeNameToId", "cancelTravelerSurname"); 
                const cancelTravelerDateOfBirthId: string = isCancelTravelCalculationTypeFullData === false ? this.mapKeyToId("mapAttributeNameToId", "cancelTravelerDateOfBirth") : this.mapKeyToId("mapAttributeNameToId", "cancelTravelerDateOfBirth");

                const cancelTravelInsurerFirstNameId: string = this.mapKeyToId("mapAttributeNameToId", "cancelTravelInsurerFirstName");
                const cancelTravelInsurerSurnameId: string = this.mapKeyToId("mapAttributeNameToId", "cancelTravelInsurerSurname");
                const cancelTravelInsurerPeselId: string = this.mapKeyToId("mapAttributeNameToId", "cancelTravelInsurerPesel");

                let cancelTravelDecodePesel: any = this.inputsTypeValuePairs[cancelTravelInsurerPeselId] ? peselDecode(this.inputsTypeValuePairs[cancelTravelInsurerPeselId].toString()) : undefined;

                const cancelTravelNumberOfPeopleTravelingId: string = isCancelTravelCalculationTypeFullData === false ? this.savedMappedIdsForLaterUse.cancelNumberOfPeopleTraveling : this.savedMappedIdsForLaterUse.cancelNumberOfPeopleTraveling;

                const cancelTravelersTableIdToChange: string = isCancelTravelCalculationTypeFullData === false ? this.savedMappedIdsForLaterUse.cancelTravelersTableId : this.savedMappedIdsForLaterUse.cancelTravelersTableId;
                // const cancelTravelersTableIdToRemove: string = isCancelTravelCalculationTypeFullData === true ? this.savedMappedIdsForLaterUse.cancelTravelersTableId : this.savedMappedIdsForLaterUse.travelersTableFullDataId;
                // const cancelTravelersNumberOfPeopleTravelingToReset: string = isCancelTravelCalculationTypeFullData === true ? this.mapKeyToId("mapAttributeNameToId", "numberOfPeopleTraveling") : this.mapKeyToId("mapAttributeNameToId", "numberOfPeopleTravelingFullData");

                if((value === this.savedMappedIdsForLaterUse.insurerIsCancelTravelParticipantOption || 
                    (valueIsFromCancelTravelCalculationTypeInput && this.inputsTypeValuePairs[this.savedMappedIdsForLaterUse.insurerIsCancelTravelParticipant] === this.savedMappedIdsForLaterUse.insurerIsCancelTravelParticipantOption)) &&
                    (!this.inputsTypeValuePairs[this.savedMappedIdsForLaterUse.insurerIsCancelTravelParticipant] || 
                        (this.inputsTypeValuePairs[this.savedMappedIdsForLaterUse.insurerIsCancelTravelParticipant] !== this.savedMappedIdsForLaterUse.insurerIsCancelTravelParticipantOption ||
                        (valueIsFromCancelTravelCalculationTypeInput && value !== this.prevTravelTypeOfCalculation)) ||
                        this.initDataCatchedForCustomInputs.insurerIsCancelTravelParticipant === false)
                ) {
                    let addNewRow: boolean = false;

                    if(valueIsFromCancelTravelCalculationTypeInput) {
                        this.prevTravelTypeOfCalculation = typeof value === 'number' ? value.toString() : value;
                    }

                    if(isJsonString(this.inputsTypeValuePairs[cancelTravelersTableIdToChange])) {
                        const parsedTableData: any = JSON.parse(this.inputsTypeValuePairs[cancelTravelersTableIdToChange]);
                        let newObjToPush: any = undefined;

                        if(isCancelTravelCalculationTypeFullData) {
                            newObjToPush = {
                                // [travelerNumberId]: this.inputsTypeValuePairs[numberOfPeopleTravelingId],
                                // [travelerFirstNameId]: this.inputsTypeValuePairs[cancelTravelInsurerFirstNameId],
                                // [travelerSurnameId]: this.inputsTypeValuePairs[travelInsurerSurnameId],
                                // [travelerPeselFullDataId]: this.inputsTypeValuePairs[travelInsurerPeselId],
                                // [travelerCityFullDataId]: this.inputsTypeValuePairs[travelInsurerCityId],
                                // [travelerStreetFullDataId]: this.inputsTypeValuePairs[travelInsurerStreetId],
                                // [travelerHouseNumberFullDataId]: this.inputsTypeValuePairs[travelInsurerHouseNumberId],
                                // [travelerPostCodeFullDataId]: this.inputsTypeValuePairs[travelInsurerPostCodeId],
                                // [travelerCountyFullDataId]: this.inputsTypeValuePairs[travelInsurerCountyId],
                                // [travelerCountryFullDataId]: this.inputsTypeValuePairs[travelInsurerCountryId],
                                // [travelerPhoneNumberId]: this.inputsTypeValuePairs[travelInsurerMobilePhoneId],
                            };
                        } else {
                            newObjToPush = {
                                // [travelerNumberId]: this.inputsTypeValuePairs[numberOfPeopleTravelingId],
                                [cancelTravelerFirstNameId]: this.inputsTypeValuePairs[cancelTravelInsurerFirstNameId],
                                [cancelTravelerSurnameId]: this.inputsTypeValuePairs[cancelTravelInsurerSurnameId],
                                [cancelTravelerDateOfBirthId]: cancelTravelDecodePesel && cancelTravelDecodePesel.date ? cancelTravelDecodePesel.date : '',
                            };
                        }

                        if(!this.inputsTypeValuePairs[cancelTravelersTableIdToChange].includes(JSON.stringify(newObjToPush).substring(1).slice(0, -15))) {
                            newObjToPush[cancelTravelerNumberId] = parseInt(this.inputsTypeValuePairs[cancelTravelNumberOfPeopleTravelingId]) > 0 ? 
                                                                (parseInt(this.inputsTypeValuePairs[cancelTravelNumberOfPeopleTravelingId]) + 1).toString() : "1";
                            parsedTableData[Object.keys(parsedTableData).length] = newObjToPush;
                            this.inputsTypeValuePairs[cancelTravelersTableIdToChange] = JSON.stringify(parsedTableData);
                            addNewRow = true;
                        }
                    } else {
                        addNewRow = true;

                        if(isCancelTravelCalculationTypeFullData) {
                            // this.inputsTypeValuePairs[cancelTravelersTableIdToChange] = JSON.stringify({0: {
                            //     [travelerNumberId]: parseInt(this.inputsTypeValuePairs[numberOfPeopleTravelingId]) > 0 ? 
                            //                             (parseInt(this.inputsTypeValuePairs[numberOfPeopleTravelingId]) + 1).toString() : "1",
                            //     [travelerFirstNameId]: this.inputsTypeValuePairs[travelInsurerFirstNameId],
                            //     [travelerSurnameId]: this.inputsTypeValuePairs[travelInsurerSurnameId],
                            //     [travelerPeselFullDataId]: this.inputsTypeValuePairs[travelInsurerPeselId],
                            //     [travelerCityFullDataId]: this.inputsTypeValuePairs[travelInsurerCityId],
                            //     [travelerStreetFullDataId]: this.inputsTypeValuePairs[travelInsurerStreetId],
                            //     [travelerHouseNumberFullDataId]: this.inputsTypeValuePairs[travelInsurerHouseNumberId],
                            //     [travelerPostCodeFullDataId]: this.inputsTypeValuePairs[travelInsurerPostCodeId],
                            //     [travelerCountyFullDataId]: this.inputsTypeValuePairs[travelInsurerCountyId],
                            //     [travelerCountryFullDataId]: this.inputsTypeValuePairs[travelInsurerCountryId],
                            //     [travelerPhoneNumberId]: this.inputsTypeValuePairs[travelInsurerMobilePhoneId],
                            // }});
                        } else {
                            this.inputsTypeValuePairs[cancelTravelersTableIdToChange] = JSON.stringify({0: {
                                [cancelTravelerNumberId]: parseInt(this.inputsTypeValuePairs[cancelTravelNumberOfPeopleTravelingId]) > 0 ? 
                                                        (parseInt(this.inputsTypeValuePairs[cancelTravelNumberOfPeopleTravelingId]) + 1).toString() : "1",
                                [cancelTravelerFirstNameId]: this.inputsTypeValuePairs[cancelTravelInsurerFirstNameId],
                                [cancelTravelerSurnameId]: this.inputsTypeValuePairs[cancelTravelInsurerSurnameId],
                                [cancelTravelerDateOfBirthId]: cancelTravelDecodePesel && cancelTravelDecodePesel.date ? cancelTravelDecodePesel.date : '',
                            }});
                        }
                    }

                    if(addNewRow === true) {
                        if(!this.inputsTypeValuePairs[cancelTravelNumberOfPeopleTravelingId] || this.inputsTypeValuePairs[cancelTravelNumberOfPeopleTravelingId].length === 0 || 
                            (typeof this.inputsTypeValuePairs[cancelTravelNumberOfPeopleTravelingId] === 'string' && this.inputsTypeValuePairs[cancelTravelNumberOfPeopleTravelingId] === '0') ||
                            (typeof this.inputsTypeValuePairs[cancelTravelNumberOfPeopleTravelingId] === 'number' && this.inputsTypeValuePairs[cancelTravelNumberOfPeopleTravelingId] === 0)
                        ) {
                            this.inputsTypeValuePairs[cancelTravelNumberOfPeopleTravelingId] = '1';
                        } else if(typeof this.inputsTypeValuePairs[cancelTravelNumberOfPeopleTravelingId] === 'string') {
                            this.inputsTypeValuePairs[cancelTravelNumberOfPeopleTravelingId] = (parseInt(this.inputsTypeValuePairs[cancelTravelNumberOfPeopleTravelingId]) + 1).toString();
                        } else if(typeof this.inputsTypeValuePairs[cancelTravelNumberOfPeopleTravelingId] === 'number') {
                            this.inputsTypeValuePairs[cancelTravelNumberOfPeopleTravelingId] = (this.inputsTypeValuePairs[cancelTravelNumberOfPeopleTravelingId] + 1).toString();
                        }
                    }
                    
                    this.initDataCatchedForCustomInputs = {...this.initDataCatchedForCustomInputs, insurerIsCancelTravelParticipant: true};
                }

                // delete this.inputsTypeValuePairs[travelersTableIdToRemove];
                // this.inputsTypeValuePairs[travelersNumberOfPeopleTravelingToReset] = '0';
            break;

            case this.savedMappedIdsForLaterUse.travelCountryList:
                const geographicalZoneSavedIds: any = {
                    geographicalZoneIdCOMPENSA: this.mapKeyToId("mapAttributeNameToId", "geographicalZoneCompensa") as string,
                    geographicalZoneIdBENEFIA: this.mapKeyToId("mapAttributeNameToId", "geographicalZoneBenefia") as string,
                    geographicalZoneIdUNIQA: this.mapKeyToId("mapAttributeNameToId", "geographicalZoneUniqa") as string,
                    geographicalZoneIdPROAMA: this.mapKeyToId("mapAttributeNameToId", "geographicalZoneProama") as string,
                    geographicalZoneIdGENERALI: this.mapKeyToId("mapAttributeNameToId", "geographicalZoneGenerali") as string,
                    geographicalZoneIdALLIANZ: this.mapKeyToId("mapAttributeNameToId", "geographicalZoneAllianz") as string,
                };

                let foundZonesPerInsurer: any = {};
                value = typeof value === 'number' ? value.toString() : value;

                if(!!value && value !== '[]' && isJsonString(value)) {
                    const parsedValue: any = JSON.parse(value);

                    parsedValue.forEach((element: any) => {
                        if(element.insurerGeographicalZonePairs && element.insurerGeographicalZonePairs.length > 0) {
                            for(let key in element.insurerGeographicalZonePairs) {
                                if(element.insurerGeographicalZonePairs.hasOwnProperty(key)) {
                                    const zone: any = element.insurerGeographicalZonePairs[key];

                                    if(!!zone.insurerName && !!zone.geographicalZone) {
                                        if(!foundZonesPerInsurer[zone.insurerName]) {
                                            foundZonesPerInsurer[zone.insurerName] = [];
                                        }

                                        if(!foundZonesPerInsurer[zone.insurerName].includes(zone.geographicalZone)) {
                                            foundZonesPerInsurer[zone.insurerName].push(zone.geographicalZone);
                                        }
                                    }
                                }
                            };
                        }
                    });

                    for(let key in foundZonesPerInsurer) {
                        if(foundZonesPerInsurer.hasOwnProperty(key)) {
                            if(foundZonesPerInsurer[key].includes('RestOfTheWorld')) {
                                this.inputsTypeValuePairs[geographicalZoneSavedIds[`geographicalZoneId${key.toUpperCase()}`]] = this.mapKeyToId("mapAttributeValueToOptionId", `GEOGRAPHICAL_ZONE_WORLD_${key.toUpperCase()}`);
                            } else if(foundZonesPerInsurer[key].includes('RestOfTheWorldWithoutUSA')) {
                                this.inputsTypeValuePairs[geographicalZoneSavedIds[`geographicalZoneId${key.toUpperCase()}`]] = this.mapKeyToId("mapAttributeValueToOptionId", `GEOGRAPHICAL_ZONE_WORLD_WITHOUT_USA_${key.toUpperCase()}`);
                            } else if(foundZonesPerInsurer[key].includes('Europe')) {
                                this.inputsTypeValuePairs[geographicalZoneSavedIds[`geographicalZoneId${key.toUpperCase()}`]] = this.mapKeyToId("mapAttributeValueToOptionId", `GEOGRAPHICAL_ZONE_EUROPE_${key.toUpperCase()}`);
                            } else {
                                this.inputsTypeValuePairs[geographicalZoneSavedIds[`geographicalZoneId${key.toUpperCase()}`]] = '';
                            } 
                        }
                    }
                } else {
                    for(let key in geographicalZoneSavedIds) {
                        if(geographicalZoneSavedIds.hasOwnProperty(key)) {
                            this.inputsTypeValuePairs[geographicalZoneSavedIds[key]] = '';
                        }
                    }
                }
            break;

            case this.savedMappedIdsForLaterUse.travelSportList:
                const updatedSportDisciplinePayload: any[] = []; 

                value = typeof value === 'number' ? value.toString() : value;
                if(!!value && isJsonString(value)) {
                    const parsedValue: any = JSON.parse(value);
                    
                    if(parsedValue && Array.isArray(parsedValue)) {
                        let highPerformanceCoverageCounter: number = 0;
                        let skiingOrSnowboardingCoverageCounter: number = 0;

                        parsedValue.forEach((sportDiscipline: any, index: number) => {
                            const newValue: any = {
                                'sportName': '',
                                'sportValues': [],
                            };

                            newValue.sportName = sportDiscipline.name;
                            
                            if(this.props.sportInsuranceCoverageStore?.dataSet && this.props.sportInsuranceCoverageStore?.dataSet.totalCount > 0) {
                                const filteredSportInsuranceCoverage: any = this.props.sportInsuranceCoverageStore?.dataSet.items.filter((x: any) => x.sportDisciplineId === sportDiscipline.id);
    
                                if(filteredSportInsuranceCoverage && Array.isArray(filteredSportInsuranceCoverage) && filteredSportInsuranceCoverage.length > 0) {
                                    filteredSportInsuranceCoverage.forEach((sportInsurance: any) => {
                                        if(sportInsurance.coverageType === 'HighPerformance') {
                                            highPerformanceCoverageCounter++;
                                        }
                                        if(sportInsurance.coverageType === 'SkiingOrSnowboarding') {
                                            skiingOrSnowboardingCoverageCounter++;
                                        }

                                        newValue.sportValues.push({
                                            'insurer': sportInsurance.insurer ? sportInsurance.insurer.name : '',
                                            'value': sportInsurance.coverageType,
                                        });
                                    });
                                }
                            }

                            let baseValueToSpread: any = {...this.inputsTypeValuePairs[this.mapKeyToId("mapAttributeNameToId", "travelAdditionalOptions")]};

                            if(highPerformanceCoverageCounter === 3 && skiingOrSnowboardingCoverageCounter === 2) {
                                baseValueToSpread = {...baseValueToSpread,
                                    [this.mapKeyToId("mapAttributeValueToOptionId", "PRO_SPORT")]: true,
                                    [this.mapKeyToId("mapAttributeValueToOptionId", "SKI_SNOW")]: true
                                };
                            } else if(highPerformanceCoverageCounter === 3) {
                                baseValueToSpread = {...baseValueToSpread,
                                    [this.mapKeyToId("mapAttributeValueToOptionId", "PRO_SPORT")]: true,
                                    [this.mapKeyToId("mapAttributeValueToOptionId", "SKI_SNOW")]: false,
                                };
                            } else if(skiingOrSnowboardingCoverageCounter === 2) {
                                baseValueToSpread = {...baseValueToSpread,
                                    [this.mapKeyToId("mapAttributeValueToOptionId", "SKI_SNOW")]: true,
                                    [this.mapKeyToId("mapAttributeValueToOptionId", "PRO_SPORT")]: false,
                                };
                            } else {
                                baseValueToSpread = {...baseValueToSpread,
                                    [this.mapKeyToId("mapAttributeValueToOptionId", "SKI_SNOW")]: false,
                                    [this.mapKeyToId("mapAttributeValueToOptionId", "PRO_SPORT")]: false,
                                };
                            }

                            this.inputsTypeValuePairs[this.mapKeyToId("mapAttributeNameToId", "travelAdditionalOptions")] = baseValueToSpread;
                            
                            sportDiscipline['sportPayload'] = newValue;
                            updatedSportDisciplinePayload.push(sportDiscipline);
                        });

                        this.setDelayedInputNewValue[this.savedMappedIdsForLaterUse.travelSportList] = JSON.stringify(updatedSportDisciplinePayload);
                    }
                }
            break;
            case this.savedMappedIdsForLaterUse.autoProductionYear:
                value = typeof value === 'number' ? value.toString() : value;

                if(!!this.inputsTypeValuePairs[this.savedMappedIdsForLaterUse.autoFirstRegistrationDate]) {
                    const productionYearDate: Date = new Date(`01-01-${value}`);
                    const firstRegistrationDate: Date = new Date(this.inputsTypeValuePairs[this.savedMappedIdsForLaterUse.autoFirstRegistrationDate]);
                    if(productionYearDate.getTime() > firstRegistrationDate.getTime()) {
                        this.catchError(L('The date of first registration cannot be earlier than the production year.'), 'other', false, MessageBarType.error, true);
                    } else {
                        this.catchError('', 'other', true, MessageBarType.error, false);
                    }
                } else if(this.state.blockNextStepButton) {
                    this.catchError('', 'other', true, MessageBarType.error, false);
                }

                if(!!value && !isNaN(parseInt(value)) && value !== this.inputsTypeValuePairs[this.savedMappedIdsForLaterUse.autoProductionYear]) {
                    const tempDropdownOptions: IDropdownOption[] = [];

                    for(let i: number = parseInt(value); i <= new Date().getFullYear(); i++) {
                        tempDropdownOptions.push({
                            key: i, 
                            text: i.toString()
                        });
                    }

                    this.customPresettedInputOptions[this.savedMappedIdsForLaterUse.autoYearOfPurchaseOfTheVehicle] = tempDropdownOptions as IDropdownOption[];
                }
            break;
            case this.savedMappedIdsForLaterUse.autoFirstRegistrationDate:
                if(!!value) {
                    const productionYearDate: Date = new Date(`01-01-${this.inputsTypeValuePairs[this.savedMappedIdsForLaterUse.autoProductionYear]}`);
                    const firstRegistrationDate: Date = new Date(value);
                    if(productionYearDate.getTime() > firstRegistrationDate.getTime()) {
                        this.catchError(L('The date of first registration cannot be earlier than the production year.'), 'other', false, MessageBarType.error, true);
                    } else {
                        this.catchError('', 'other', true, MessageBarType.error, false);
                    }
                } else if(this.state.blockNextStepButton) {
                    this.catchError('', 'other', true, MessageBarType.error, false);
                }
            break;
            case this.savedMappedIdsForLaterUse.autoVin:
                value = typeof value === 'number' ? value.toString() : value;

                if(!!value && value.length === 17 && value !== this.eurotaxInfoexpertFormData.vin) {
                    this.eurotaxInfoexpertFormData.vin = value;
                    this.getVehicleConfigByVin(value);
                }
            break;
            case this.savedMappedIdsForLaterUse.childNnwChildPesel:
                value = typeof value === 'number' ? value.toString() : value;
                
                if(!!value) {
                    if(value.match(/^[0-9]+$/) !== null) {
                        if(validatePesel(value) === false) {
                            this.inputErrorsText[id] = L('Pesel is not valid.');
                            this.blockNextStepButton = true;
                        } else {
                            delete this.inputErrorsText[id];
                            this.blockNextStepButton = false;
                        }
                    } else {
                        this.inputErrorsText[id] = L('Pesel number must consist of only 11 digits.');
                        this.blockNextStepButton = true;
                    }
                } else if(this.state.blockNextStepButton) {
                    delete this.inputErrorsText[id];
                    this.blockNextStepButton = false;
                }
            break;
            case this.savedMappedIdsForLaterUse.cancelNumberOfPeopleTraveling:
                if(!!value) {
                    let minValue: number = 1;
                    let maxValue: number = 10;

                    if(this.inputsIdUserFieldsPairs[id]) {
                        this.inputsIdUserFieldsPairs[id].forEach((userField: any) => {
                            if(userField.Key === 'min' && !!userField.Value) {
                                minValue = typeof userField.Value === 'string' ? parseInt(userField.Value) : userField.Value;
                            }

                            if(userField.Key === 'max' && !!userField.Value) {
                                maxValue = typeof userField.Value === 'string' ? parseInt(userField.Value) : userField.Value;
                            }
                        });
                    }

                    const valueNumber: number = typeof value === 'string' ? parseInt(value) : value;
                    const currentCancelTravelInsuranceSum: number = this.inputsTypeValuePairs[this.savedMappedIdsForLaterUse.cancelTravelInsuranceSum] ?
                        (typeof this.inputsTypeValuePairs[this.savedMappedIdsForLaterUse.cancelTravelInsuranceSum] === 'string' ? 
                            parseFloat(this.inputsTypeValuePairs[this.savedMappedIdsForLaterUse.cancelTravelInsuranceSum]) 
                            : 
                            this.inputsTypeValuePairs[this.savedMappedIdsForLaterUse.cancelTravelInsuranceSum]
                        ) 
                        : 0;

                    if(valueNumber < minValue || valueNumber > maxValue) {
                        this.inputErrorsText[id] = `${L('Number of people traveling must be within the range.')} ${L('Minimum')}: ${minValue}, ${L('Maximum')}: ${maxValue}.`;
                        this.blockNextStepButton = true;
                    } else {
                        delete this.inputErrorsText[id];
                        this.blockNextStepButton = false;
                    }

                    if(currentCancelTravelInsuranceSum < 0 || currentCancelTravelInsuranceSum > valueNumber * 35000) {
                        this.inputErrorsText[this.savedMappedIdsForLaterUse.cancelTravelInsuranceSum] = `${L('The sum insured exceeds the allowable value.')} ${L('Maximum')}: ${valueNumber * 35000} PLN.`;
                        this.blockNextStepButton = true;
                    } else {
                        delete this.inputErrorsText[this.savedMappedIdsForLaterUse.cancelTravelInsuranceSum];
                        this.blockNextStepButton = false;
                    }
                } else if(this.state.blockNextStepButton) {
                    delete this.inputErrorsText[this.savedMappedIdsForLaterUse.cancelTravelInsuranceSum];
                    this.blockNextStepButton = false;
                }
            break;
            case this.savedMappedIdsForLaterUse.cancelTravelInsuranceSum:
                if(!!value) {
                    const valueNumber: number = typeof value === 'string' ? parseFloat(value) : value;
                    const cancelTravelNumberOfPeopleTravelingId: string = this.savedMappedIdsForLaterUse.cancelNumberOfPeopleTraveling;
                    const currentCancelNumberOfTravelers: number = this.inputsTypeValuePairs[cancelTravelNumberOfPeopleTravelingId] ?
                        (typeof this.inputsTypeValuePairs[cancelTravelNumberOfPeopleTravelingId] === 'string' ? 
                            parseInt(this.inputsTypeValuePairs[cancelTravelNumberOfPeopleTravelingId]) 
                            : 
                            this.inputsTypeValuePairs[cancelTravelNumberOfPeopleTravelingId]
                        ) 
                        : 0;
                    if(valueNumber < 0 || valueNumber > currentCancelNumberOfTravelers * 35000) {
                        this.inputErrorsText[id] = `${L('The sum insured exceeds the allowable value.')} ${L('Maximum')}: ${currentCancelNumberOfTravelers * 35000} PLN.`;
                        this.blockNextStepButton = true;
                    } else {
                        delete this.inputErrorsText[id];
                        this.blockNextStepButton = false;
                    }
                } else if(this.state.blockNextStepButton) {
                    delete this.inputErrorsText[id];
                    this.blockNextStepButton = false;
                }
            break;
            case this.savedMappedIdsForLaterUse.cancelTravelPurchaseDate:
                if(moment(value).isValid()) {
                    if(!this.customInputsData[this.savedMappedIdsForLaterUse.cancelTravelStartDate]) {
                        this.customInputsData[this.savedMappedIdsForLaterUse.cancelTravelStartDate] = {};
                    }

                    if(!this.customInputsData[this.savedMappedIdsForLaterUse.cancelTravelEndDate]) {
                        this.customInputsData[this.savedMappedIdsForLaterUse.cancelTravelEndDate] = {};
                    }

                    let momentDate: Moment = moment(value);
                    let now: Moment = moment();
                    
                    momentDate = momentDate.set('hour', 0).set('minute', 0).set('second', 0).set('millisecond', 0);
                    now = now.set('hour', 0).set('minute', 0).set('second', 0).set('millisecond', 0);
                    
                    let nowSub3: Moment = moment().subtract(3, 'days').set('hour', 0).set('minute', 0).set('second', 0).set('millisecond', 0);
                    let nowSub10: Moment = moment().subtract(10, 'days').set('hour', 0).set('minute', 0).set('second', 0).set('millisecond', 0);
                    let nowAdd1: Moment = moment().add(1, 'days').set('hour', 0).set('minute', 0).set('second', 0).set('millisecond', 0);
                    let nowAdd7: Moment = moment().add(7, 'days').set('hour', 0).set('minute', 0).set('second', 0).set('millisecond', 0);
                    let nowAdd30: Moment = moment().add(30, 'days').set('hour', 0).set('minute', 0).set('second', 0).set('millisecond', 0);
                    let purchaseDateAdd31: Moment = momentDate.clone().add(31, 'days').set('hour', 0).set('minute', 0).set('second', 0).set('millisecond', 0);

                    if(momentDate.isSame(now, 'day')) {
                        this.customInputsData[this.savedMappedIdsForLaterUse.cancelTravelStartDate].validationData = [
                            {Key: 'minDate', Value: nowAdd1}
                        ];
                        this.customInputsData[this.savedMappedIdsForLaterUse.cancelTravelEndDate].validationData = [
                            {Key: 'minDate', Value: nowAdd1}
                        ];
                    } else if(momentDate.isBetween(nowSub3, now, 'day') || momentDate.isSame(nowSub3, 'day')) {
                        this.customInputsData[this.savedMappedIdsForLaterUse.cancelTravelStartDate].validationData = [
                            {Key: 'minDate', Value: nowAdd7}
                        ];
                        this.customInputsData[this.savedMappedIdsForLaterUse.cancelTravelEndDate].validationData = [
                            {Key: 'minDate', Value: nowAdd7}
                        ];
                    } else if(momentDate.isBetween(nowSub10, now, 'day') || momentDate.isSame(nowSub10, 'day')) {
                        this.customInputsData[this.savedMappedIdsForLaterUse.cancelTravelStartDate].validationData = [
                            {Key: 'minDate', Value: purchaseDateAdd31}
                        ];
                        this.customInputsData[this.savedMappedIdsForLaterUse.cancelTravelEndDate].validationData = [
                            {Key: 'minDate', Value: purchaseDateAdd31}
                        ];
                    } 
                    // else if(momentDate.isBefore(nowSub3, 'day')) {
                    //     this.customInputsData[this.savedMappedIdsForLaterUse.cancelTravelStartDate].validationData = [
                    //         {Key: 'minDate', Value: nowAdd30}
                    //     ];
                    //     this.customInputsData[this.savedMappedIdsForLaterUse.cancelTravelEndDate].validationData = [
                    //         {Key: 'minDate', Value: nowAdd30}
                    //     ];
                    // } 
                    else {
                        this.customInputsData[this.savedMappedIdsForLaterUse.cancelTravelStartDate].validationData = [
                            {Key: 'minDate', Value: nowAdd30}
                        ];
                        this.customInputsData[this.savedMappedIdsForLaterUse.cancelTravelEndDate].validationData = [
                            {Key: 'minDate', Value: nowAdd30}
                        ];
                    }
                }
            break;
            case this.savedMappedIdsForLaterUse.travelStartDateId:
            case this.savedMappedIdsForLaterUse.travelEndDateId:
            case this.savedMappedIdsForLaterUse.autoStartDateId:
            case this.savedMappedIdsForLaterUse.nnwdStartDateId:
            case this.savedMappedIdsForLaterUse.cancelTravelStartDate:
                if(id === this.savedMappedIdsForLaterUse.cancelTravelStartDate && moment(value).isValid()) {
                    if(!this.customInputsData[this.savedMappedIdsForLaterUse.cancelTravelEndDate]) {
                        this.customInputsData[this.savedMappedIdsForLaterUse.cancelTravelEndDate] = {};
                    }
                    this.customInputsData[this.savedMappedIdsForLaterUse.cancelTravelEndDate].validationData = [
                        {Key: 'minDate', Value: value}
                    ];
                }

                if(!!value && this.isEditMode && [
                        this.savedMappedIdsForLaterUse.travelStartDateId,
                        this.savedMappedIdsForLaterUse.autoStartDateId,
                        this.savedMappedIdsForLaterUse.nnwdStartDateId,
                        this.savedMappedIdsForLaterUse.cancelTravelStartDate,
                    ].includes(id)
                ) {
                    const today = new Date(Date.now());
                    const valueDate = moment(value);
                    if(valueDate.isValid() && valueDate.isBefore(moment(today))) {
                        this.inputsTypeValuePairs[id] = today.toUTCString();
                        this.setDelayedInputNewValue[id] = today.toUTCString();
                    }
                } else if(!!value) {
                    const today = new Date(Date.now());
                    const valueDate = moment(value);

                    if(valueDate.isValid() && valueDate.isBefore(moment(today))) {
                        this.inputsTypeValuePairs[id] = today.toUTCString();
                        this.setDelayedInputNewValue[id] = today.toUTCString();
                    }

                    if(id === this.savedMappedIdsForLaterUse.travelStartDateId && !!this.inputsTypeValuePairs[this.savedMappedIdsForLaterUse.travelEndDateId] && 
                        valueDate.isAfter(moment(this.inputsTypeValuePairs[this.savedMappedIdsForLaterUse.travelEndDateId]))
                    ) {
                        this.inputsTypeValuePairs[this.savedMappedIdsForLaterUse.travelEndDateId] = valueDate;
                        this.setDelayedInputNewValue[this.savedMappedIdsForLaterUse.travelEndDateId] = valueDate;
                    }

                    if(id === this.savedMappedIdsForLaterUse.travelEndDateId && !!this.inputsTypeValuePairs[this.savedMappedIdsForLaterUse.travelStartDateId] && 
                        valueDate.isBefore(moment(this.inputsTypeValuePairs[this.savedMappedIdsForLaterUse.travelStartDateId]))
                    ) {
                        this.inputsTypeValuePairs[id] = this.inputsTypeValuePairs[this.savedMappedIdsForLaterUse.travelStartDateId];
                        this.setDelayedInputNewValue[id] = this.inputsTypeValuePairs[this.savedMappedIdsForLaterUse.travelStartDateId];
                    }
                }
            break;
            case this.savedMappedIdsForLaterUse.nnwdInsurerIsInsured:
                if(value === this.savedMappedIdsForLaterUse.nnwdInsuredIsInsurerOption) { 
                    this.insurerIsInsured = false;

                    if(this.clientTypeValuePairs) {
                        this.inputsTypeValuePairs[this.mapKeyToId("mapAttributeNameToId", "nnwChildrenFirstName")] = this.clientTypeValuePairs.insurer['Nnwd.Insurer.FirstName'];
                        this.inputsTypeValuePairs[this.mapKeyToId("mapAttributeNameToId", "nnwChildrenLastName")] = this.clientTypeValuePairs.insurer['Nnwd.Insurer.Surname'];
                        this.inputsTypeValuePairs[this.mapKeyToId("mapAttributeNameToId", "childNnwChildPesel")] = this.clientTypeValuePairs.insurer['Nnwd.Insurer.Pesel'];
                    }
                } else {
                    this.insurerIsInsured = true;
                }
            break;
        }

        if(caseFoundFlag === false) {
        //     if(id === this.mapAttributeNameToId('productionYear')) {
        //         this.customInputsData['productionYear'] = value;
        //         delete this.customInputsData['vehicleBrand'];
        //         delete this.customInputsData['vehicleModel'];
        //     }
    
        //     if(id === this.mapAttributeNameToId('vehicleType') && this.props.productAttributeStore && this.props.productAttributeStore.dataSet && this.props.productAttributeStore.dataSet.totalCount > 0) {
        //         let attrData = getAttributeNameAndValue(this.product, this.props.productAttributeStore?.dataSet.items, id, value, this.gnLanguage, this.getMapNameByProduct(this.product));
        //         this.customInputsData['vehicleType'] = attrData.value;
        //     }
        }
    }

    private toggleMessageBar(which: string, type: MessageBarType, text: string, hide: boolean) {
        if(which === "other") {
            this.setState((prevState) => ({ ...prevState, message: { text: text, type: type ? type : MessageBarType.error } }));
        } else {
            this.summaryMessageBoxData[which].text = text;
            this.summaryMessageBoxData[which].type = type;
            this.summaryMessageBoxData[which].hide = hide;
        }
    }

    private catchError(error: any, callType: string, hide?: boolean, type?: MessageBarType, blockNextStepButton?: boolean) {
        let text = catchErrorMessage(error);

        if(callType === "other") {
            this.setState((prevState) => ({ ...prevState, message: (hide === true ? undefined : { text: text, type: !!type ? type : MessageBarType.error }),
                                            blockNextStepButton: typeof blockNextStepButton === 'boolean' ? blockNextStepButton : prevState.blockNextStepButton }));
        } else {
            this.summaryMessageBoxData[callType].text = text;
            this.summaryMessageBoxData[callType].type = MessageBarType.error;
            this.summaryMessageBoxData[callType].hide = false;
        }
    }

    private cloneProductAttributeMappingsData(productAttributeMappingsToChange: any[], productAttributeMappingsToClone: any[], unsetAllValues?: boolean) {
        this.savedProductAttributeMappings = {
            productAttributeMappingsToChange: productAttributeMappingsToChange,
            productAttributeMappingsToWatch: productAttributeMappingsToClone,
        };
        
        productAttributeMappingsToChange.forEach((productAttributeMappingToChange: any) => {
            let tempUserFields: any[] = [];

            if(productAttributeMappingToChange.ProductAttribute && productAttributeMappingToChange.ProductAttribute.UserFields) {
                tempUserFields = productAttributeMappingToChange.ProductAttribute.UserFields;
            } else if(productAttributeMappingToChange && productAttributeMappingToChange.UserFields) {
                tempUserFields = productAttributeMappingToChange.UserFields;
            }

            tempUserFields.some((UserField: any) => {
                if(UserField.Key === 'key') {
                    let splitedValue: string[] = UserField.Value.split('.');
                    let attributeIdOfClonedMapping: string = mapAttributeKeyToId(productAttributeMappingsToClone, splitedValue[splitedValue.length - 1]);
                    let valueToCopy: string = this.inputsTypeValuePairs[attributeIdOfClonedMapping];

                    if(!!valueToCopy) {
                        this.inputsTypeValuePairs[productAttributeMappingToChange.Id] = getValueBasedOnClonedValue(valueToCopy, productAttributeMappingToChange, productAttributeMappingsToClone, attributeIdOfClonedMapping);
                        this.inputsIdUserFieldsPairs[productAttributeMappingToChange.Id] = tempUserFields;
                    }

                    if(unsetAllValues === true) {
                        delete this.inputsTypeValuePairs[productAttributeMappingToChange.Id];
                        this.inputsIdUserFieldsPairs[productAttributeMappingToChange.Id] = tempUserFields;
                    }

                    return true;
                }
                return false;
            });
        });

        this.controlledForceUpdate('line 419');
    }

    private async fillFormWithSelectedClientData(productAttributeMappingsToChange: any[], calledFromInside: boolean = false) {
        this.toggleAsyncActionFlag(true);
        
        if(this.tempSelectedClient && this.tempSelectedClient.length > 0) {
            let clientFound: boolean = false;
            let client: ClientDto = defaultClient; 
            
            if(this.selectedClientData && parseInt(this.tempSelectedClient) !== parseInt(this.selectedClientData.id)) {
                await this.props.clientStore?.get({...defaultClient, id: this.tempSelectedClient}).then((response: ClientDto) => {
                    client = response;
                    this.selectedClientData = response;
                    clientFound = true; 
                }).catch((error: any) => {
                    this.catchError(error, "other");
                });
            } else if (this.selectedClientData) {
                client = this.selectedClientData;
                clientFound = true;
            }

            if(mapAttributeKeyToId(productAttributeMappingsToChange, 'FirstName').length > 0) {
                const attrId: string = mapAttributeKeyToId(productAttributeMappingsToChange, 'FirstName');
                this.inputsIdUserFieldsPairs[attrId] = getUserFieldsFromProductAttributeMapping(productAttributeMappingsToChange, attrId);
                this.inputsTypeValuePairs[attrId] = client.user.name;
                this.catchDataForCustomerInputs(attrId, this.inputsTypeValuePairs[attrId], this.inputsIdUserFieldsPairs[attrId]);
            }
            if(mapAttributeKeyToId(productAttributeMappingsToChange, 'Surname').length > 0) {
                const attrId: string = mapAttributeKeyToId(productAttributeMappingsToChange, 'Surname');
                this.inputsIdUserFieldsPairs[attrId] = getUserFieldsFromProductAttributeMapping(productAttributeMappingsToChange, attrId);
                this.inputsTypeValuePairs[attrId] = client.user.surname;
                this.catchDataForCustomerInputs(attrId, this.inputsTypeValuePairs[attrId], this.inputsIdUserFieldsPairs[attrId]);
            }
            if(mapAttributeKeyToId(productAttributeMappingsToChange, 'Name').length > 0) {
                const attrId: string = mapAttributeKeyToId(productAttributeMappingsToChange, 'Name');
                this.inputsIdUserFieldsPairs[attrId] = getUserFieldsFromProductAttributeMapping(productAttributeMappingsToChange, attrId);
                this.inputsTypeValuePairs[attrId] = client.countryId;
                this.catchDataForCustomerInputs(attrId, this.inputsTypeValuePairs[attrId], this.inputsIdUserFieldsPairs[attrId]);
            }
            if(mapAttributeKeyToId(productAttributeMappingsToChange, 'CompanyName').length > 0) {
                const attrId: string = mapAttributeKeyToId(productAttributeMappingsToChange, 'CompanyName');
                this.inputsIdUserFieldsPairs[attrId] = getUserFieldsFromProductAttributeMapping(productAttributeMappingsToChange, attrId);
                this.inputsTypeValuePairs[attrId] = client.company;
                this.catchDataForCustomerInputs(attrId, this.inputsTypeValuePairs[attrId], this.inputsIdUserFieldsPairs[attrId]);
            }
            if(mapAttributeKeyToId(productAttributeMappingsToChange, 'Nip').length > 0) {
                const attrId: string = mapAttributeKeyToId(productAttributeMappingsToChange, 'Nip');
                this.inputsIdUserFieldsPairs[attrId] = getUserFieldsFromProductAttributeMapping(productAttributeMappingsToChange, attrId);
                this.inputsTypeValuePairs[attrId] = client.nip;
                this.catchDataForCustomerInputs(attrId, this.inputsTypeValuePairs[attrId], this.inputsIdUserFieldsPairs[attrId]);
            }
            if(mapAttributeKeyToId(productAttributeMappingsToChange, 'Regon').length > 0) {
                const attrId: string = mapAttributeKeyToId(productAttributeMappingsToChange, 'Regon');
                this.inputsIdUserFieldsPairs[attrId] = getUserFieldsFromProductAttributeMapping(productAttributeMappingsToChange, attrId);
                this.inputsTypeValuePairs[attrId] = client.regon;
                this.catchDataForCustomerInputs(attrId, this.inputsTypeValuePairs[attrId], this.inputsIdUserFieldsPairs[attrId]);
            }
            if(mapAttributeKeyToId(productAttributeMappingsToChange, 'City').length > 0) {
                const attrId: string = mapAttributeKeyToId(productAttributeMappingsToChange, 'City');
                this.inputsIdUserFieldsPairs[attrId] = getUserFieldsFromProductAttributeMapping(productAttributeMappingsToChange, attrId);
                this.inputsTypeValuePairs[attrId] = client.city;
                this.catchDataForCustomerInputs(attrId, this.inputsTypeValuePairs[attrId], this.inputsIdUserFieldsPairs[attrId]);
            }
            if(mapAttributeKeyToId(productAttributeMappingsToChange, 'Street').length > 0) {
                const attrId: string = mapAttributeKeyToId(productAttributeMappingsToChange, 'Street');
                this.inputsIdUserFieldsPairs[attrId] = getUserFieldsFromProductAttributeMapping(productAttributeMappingsToChange, attrId);
                this.inputsTypeValuePairs[attrId] = client.streetAddress;
                this.catchDataForCustomerInputs(attrId, this.inputsTypeValuePairs[attrId], this.inputsIdUserFieldsPairs[attrId]);
            }
            if(mapAttributeKeyToId(productAttributeMappingsToChange, 'HouseNumber').length > 0) {
                const attrId: string = mapAttributeKeyToId(productAttributeMappingsToChange, 'HouseNumber');
                this.inputsIdUserFieldsPairs[attrId] = getUserFieldsFromProductAttributeMapping(productAttributeMappingsToChange, attrId);
                this.inputsTypeValuePairs[attrId] = client.streetAddress2;
                this.catchDataForCustomerInputs(attrId, this.inputsTypeValuePairs[attrId], this.inputsIdUserFieldsPairs[attrId]);
            }
            if(mapAttributeKeyToId(productAttributeMappingsToChange, 'Birthdate').length > 0) {
                const attrId: string = mapAttributeKeyToId(productAttributeMappingsToChange, 'Birthdate');
                this.inputsIdUserFieldsPairs[attrId] = getUserFieldsFromProductAttributeMapping(productAttributeMappingsToChange, attrId);
                this.inputsTypeValuePairs[attrId] = client.dateOfBirth;
                this.catchDataForCustomerInputs(attrId, this.inputsTypeValuePairs[attrId], this.inputsIdUserFieldsPairs[attrId]);
            }
            if(mapAttributeKeyToId(productAttributeMappingsToChange, 'MobilePhone').length > 0) {
                const attrId: string = mapAttributeKeyToId(productAttributeMappingsToChange, 'MobilePhone');
                this.inputsIdUserFieldsPairs[attrId] = getUserFieldsFromProductAttributeMapping(productAttributeMappingsToChange, attrId);
                this.inputsTypeValuePairs[attrId] = client.phone;
                this.catchDataForCustomerInputs(attrId, this.inputsTypeValuePairs[attrId], this.inputsIdUserFieldsPairs[attrId]);
            }
            if(mapAttributeKeyToId(productAttributeMappingsToChange, 'PostCode').length > 0) {
                const attrId: string = mapAttributeKeyToId(productAttributeMappingsToChange, 'PostCode');
                this.inputsIdUserFieldsPairs[attrId] = getUserFieldsFromProductAttributeMapping(productAttributeMappingsToChange, attrId);
                this.inputsTypeValuePairs[attrId] = client.zipPostalCode;
                this.catchDataForCustomerInputs(attrId, this.inputsTypeValuePairs[attrId], this.inputsIdUserFieldsPairs[attrId]);
            }
            if(mapAttributeKeyToId(productAttributeMappingsToChange, 'Pesel').length > 0) {
                const attrId: string = mapAttributeKeyToId(productAttributeMappingsToChange, 'Pesel');
                this.inputsIdUserFieldsPairs[attrId] = getUserFieldsFromProductAttributeMapping(productAttributeMappingsToChange, attrId);
                this.inputsTypeValuePairs[attrId] = client.pesel;
                this.catchDataForCustomerInputs(attrId, this.inputsTypeValuePairs[attrId], this.inputsIdUserFieldsPairs[attrId]);
            }
            if(mapAttributeKeyToId(productAttributeMappingsToChange, 'Country').length > 0) {
                const attrId: string = mapAttributeKeyToId(productAttributeMappingsToChange, 'Country');
                this.inputsIdUserFieldsPairs[attrId] = getUserFieldsFromProductAttributeMapping(productAttributeMappingsToChange, attrId);
                this.inputsTypeValuePairs[attrId] = client.country;
                this.catchDataForCustomerInputs(attrId, this.inputsTypeValuePairs[attrId], this.inputsIdUserFieldsPairs[attrId]);
            }
            if(mapAttributeKeyToId(productAttributeMappingsToChange, 'County').length > 0) {
                const attrId: string = mapAttributeKeyToId(productAttributeMappingsToChange, 'County');
                this.inputsIdUserFieldsPairs[attrId] = getUserFieldsFromProductAttributeMapping(productAttributeMappingsToChange, attrId);
                this.inputsTypeValuePairs[attrId] = client.county;
                this.catchDataForCustomerInputs(attrId, this.inputsTypeValuePairs[attrId], this.inputsIdUserFieldsPairs[attrId]);
            }
            if(mapAttributeKeyToId(productAttributeMappingsToChange, 'Email').length > 0) {
                const attrId: string = mapAttributeKeyToId(productAttributeMappingsToChange, 'Email');
                this.inputsIdUserFieldsPairs[attrId] = getUserFieldsFromProductAttributeMapping(productAttributeMappingsToChange, attrId);
                this.inputsTypeValuePairs[attrId] = client.user.emailAddress;
                this.catchDataForCustomerInputs(attrId, this.inputsTypeValuePairs[attrId], this.inputsIdUserFieldsPairs[attrId]);
            }
            if(mapAttributeKeyToId(productAttributeMappingsToChange, 'AdditionalEmail').length > 0) {
                const attrId: string = mapAttributeKeyToId(productAttributeMappingsToChange, 'AdditionalEmail');
                this.inputsIdUserFieldsPairs[attrId] = getUserFieldsFromProductAttributeMapping(productAttributeMappingsToChange, attrId);
                this.inputsTypeValuePairs[attrId] = client.emailAdditional;
                this.catchDataForCustomerInputs(attrId, this.inputsTypeValuePairs[attrId], this.inputsIdUserFieldsPairs[attrId]);
            }
            if(mapAttributeKeyToId(productAttributeMappingsToChange, 'ClientType').length > 0) {
                const attrId: string = mapAttributeKeyToId(productAttributeMappingsToChange, 'ClientType');
                this.inputsIdUserFieldsPairs[attrId] = getUserFieldsFromProductAttributeMapping(productAttributeMappingsToChange, attrId);
                
                let filteredProductAttributeMapping: any = filterBySome(productAttributeMappingsToChange, "Id", attrId);
                let newValue = client.clientType;
                for(let key in filteredProductAttributeMapping.ProductAttributeValues) {
                    if(filteredProductAttributeMapping.ProductAttributeValues.hasOwnProperty(key) && 
                        filteredProductAttributeMapping.ProductAttributeValues[key].Name === client.clientType)
                    {
                        newValue = filteredProductAttributeMapping.ProductAttributeValues[key].Id;
                    }
                }
                
                this.inputsTypeValuePairs[attrId] = newValue;
                this.catchDataForCustomerInputs(attrId, this.inputsTypeValuePairs[attrId], this.inputsIdUserFieldsPairs[attrId]);
            }
            if(mapAttributeKeyToId(productAttributeMappingsToChange, 'Nationality').length > 0 && !!client.nationality && client.nationality.length > 0) {
                const attrId: string = mapAttributeKeyToId(productAttributeMappingsToChange, 'Nationality');
                this.inputsIdUserFieldsPairs[attrId] = getUserFieldsFromProductAttributeMapping(productAttributeMappingsToChange, attrId);
                this.inputsTypeValuePairs[attrId] = client.nationality;
                this.catchDataForCustomerInputs(attrId, this.inputsTypeValuePairs[attrId], this.inputsIdUserFieldsPairs[attrId]);
            }

            if(clientFound === false && calledFromInside === false) {
                this.fillFormWithSelectedClientData(productAttributeMappingsToChange, true);
                return;
            } else if(calledFromInside === true) {
                this.forcedLastChangedSection = CalculationSectionType.Owner;
            }

            this.tempSelectedClient = "";
        }
        this.toggleAsyncActionFlag(false);
        this.controlledForceUpdate();
    }

    private setApkData() {
        let tempApkData: any[] = [];
        let foundApkProductAttributes: any[] = [];
        if(this.product && this.product.ProductAttributeMappings && this.productAttributes) {
            this.product.ProductAttributeMappings.forEach((productAttributeMapping: any) => {
                if(productAttributeMapping.ProductAttribute && productAttributeMapping.ProductAttribute.UserFields) {
                    productAttributeMapping.ProductAttribute.UserFields.some((UserField: any) => {
                        if(UserField.Key === 'is_for_apk') {
                            foundApkProductAttributes.push({
                                productAttributeMapping, productAttribute: productAttributeMapping.ProductAttribute
                            });
                            return true;
                        } 
                        return false;
                    });
                }
                return true;
            });
        }

        foundApkProductAttributes.forEach((apkProductAttribute) => {
            let key: string = "";
            let foundValue: any;

            apkProductAttribute.productAttribute.UserFields.some((UserField: any) => {
                if(UserField.Key === 'key') {
                    key = UserField.Value;
                    return true;
                } 
                return false;
            });

            let tempAttrValue = "";
            if(typeof this.inputsTypeValuePairs[apkProductAttribute.productAttributeMapping.Id] === 'string' || typeof this.inputsTypeValuePairs[apkProductAttribute.productAttributeMapping.Id] === 'number') {
                tempAttrValue = this.inputsTypeValuePairs[apkProductAttribute.productAttributeMapping.Id];

                apkProductAttribute.productAttributeMapping.ProductAttributeValues.some((productAttributeValue: any) => {
                    if(productAttributeValue.Id === tempAttrValue) {
                        foundValue = productAttributeValue;
                        return true;
                    }
                    return false;
                });
    
                if(!foundValue) {
                    apkProductAttribute.productAttributeMapping.ProductAttributeValues.some((productAttributeValue: any) => {
                        if(productAttributeValue.IsPreSelected === true) {
                            foundValue = productAttributeValue;
                            return true;
                        }
                        return false;
                    });
                }
    
                // if(!foundValue) {
                //     foundValue = apkProductAttribute.productAttributeMapping.ProductAttributeValues[0];
                // }

                tempApkData.push({
                    locales: [...apkProductAttribute.productAttribute.Locales],
                    key,
                    // valueId: foundValue && foundValue.Id ? foundValue.Id : "",
                    valueName: foundValue && foundValue.Name ? foundValue.Name : "",
                    valueLocales: foundValue && foundValue.Locales ? [...foundValue.Locales] : [],
                });
            } else {
                let multipleValues: string[] = [];
                let multipleValueTranslations: any[] = [];
                let getAttributeData;

                for(let jsonKey in this.inputsTypeValuePairs[apkProductAttribute.productAttributeMapping.Id]) {
                    if(this.inputsTypeValuePairs[apkProductAttribute.productAttributeMapping.Id].hasOwnProperty(jsonKey) && this.inputsTypeValuePairs[apkProductAttribute.productAttributeMapping.Id][jsonKey]) {
                        getAttributeData = getAttributeNameAndValue(this.product, this.productAttributes, jsonKey, this.inputsTypeValuePairs[apkProductAttribute.productAttributeMapping.Id][jsonKey], this.gnLanguage, this.getMapNameByProduct(this.product), {"parentKey": apkProductAttribute.productAttributeMapping.Id, "returnLocales": true});
                        multipleValues.push(getAttributeData.value);
                        let localeArrayClone: any[] = [];
                        getAttributeData.locales.forEach((locale: any) => {
                            localeArrayClone.push({...locale});
                        });
                        multipleValueTranslations.push(localeArrayClone);
                    }
                }
                
                tempAttrValue = multipleValues.join(', ');

                let concatedValueLocales: any[] = [];
                multipleValueTranslations.forEach((locales: any) => {
                    if(concatedValueLocales.length === 0) {
                        concatedValueLocales = [...locales];
                    } else {
                        concatedValueLocales.forEach((singleLocale: any, index: number) => {
                            concatedValueLocales[index].LocaleValue += `, ${locales[index].LocaleValue}`;
                        });
                    }
                });

                tempApkData.push({
                    locales: [...apkProductAttribute.productAttribute.Locales],
                    key,
                    // valueId: foundValue && foundValue.Id ? foundValue.Id : "",
                    valueName: tempAttrValue,
                    valueLocales: multipleValueTranslations ? [...concatedValueLocales] : [],
                });
            }

            this.preSettedApkData = tempApkData;
            this.forceUpdate();
        });
    }

    private async getSingleCalculation(insurerName: string) {
        if((this.selectedCalculation && this.selectedCalculation.insurerName) || insurerName) {
            this.toggleAsyncActionFlag(true);

            if(!this.selectedCalculation || !this.selectedCalculation.insurerName) {
                if(!this.selectedCalculation || typeof this.selectedCalculation !== 'object') {
                    this.selectedCalculation = {};
                }
                this.selectedCalculation['insurerName'] = insurerName;
            }
            
            this.lastGetCalculationPayload['payload'] = buildRequestBody('getCalculation', this, this.getProductTypeByProduct(this.product), this.getMapNameByProduct(this.product));
            this.lastGetCalculationPayload['insurerName'] = insurerName ? insurerName : this.selectedCalculation.insurerName;
            await policyCalculationService.getCalculation(this.lastGetCalculationPayload.payload, this.lastGetCalculationPayload.insurerName, this.getCalculationsRawResponse && this.getCalculationsRawResponse.calculationId ? this.getCalculationsRawResponse.calculationId : -1).then((response) => {
                const newPolicyCalculation: any = response.data.result.policyCalculations[0];
    
                if(this.calculations && this.calculations.data && this.calculations.data.result && this.calculations.data.result.policyCalculations) {
                    let newPolicyCalculationSet: boolean = false;
    
                    this.calculations.data.result.policyCalculations.some((policyCalculation: any, index: number) => {
                        if(policyCalculation.gnInsurerId === newPolicyCalculation.gnInsurerId) {
                            this.calculations.data.result.policyCalculations[index] = newPolicyCalculation;
                            newPolicyCalculationSet = true;
                            return true;
                        }
                        return false;
                    });

                    if(newPolicyCalculation.limitations && newPolicyCalculation.limitations.length > 0) {
                        let errorMessage: string = "";

                        newPolicyCalculation.limitations.forEach((limitation: any) => {
                            limitation.inputsLimitation.forEach((inputLimitation: any) => {
                                let foundInputId: string = '';
                                let foundInputOptionId: string = '';

                                for(let inputId in this.templateInputsForCalculationAdjust) {
                                    if(this.templateInputsForCalculationAdjust.hasOwnProperty(inputId)) {
                                        if(this.templateInputsForCalculationAdjust[inputId].userFields) {
                                            // eslint-disable-next-line
                                            this.templateInputsForCalculationAdjust[inputId].userFields.some((userField: any) => {
                                                if(userField.Key === 'key' && userField.Value === inputLimitation.key) {
                                                    foundInputId = inputId;

                                                    if(this.templateInputsForCalculationAdjust[inputId].attr.ProductAttributeValues && !!inputLimitation.optionNameValue)
                                                    this.templateInputsForCalculationAdjust[inputId].attr.ProductAttributeValues.some((productAttributeValue: any) => {
                                                        if(productAttributeValue.Name === inputLimitation.optionNameValue) {
                                                            foundInputOptionId = productAttributeValue.Id;
                                                            return true;
                                                        }
                                                        return false;
                                                    });
                                                    return true;
                                                }
                                                return false;
                                            });
                                        }
                                    }
                                    if(!!foundInputId) {
                                        break;
                                    }
                                }

                                if(!!foundInputId && !!foundInputId) {
                                    this.disabledInputOptionsIds.push(foundInputOptionId);
                                    this.inputsTypeValuePairs[foundInputId] = {...this.inputsTypeValuePairs[foundInputId], [foundInputOptionId]: false};
                                } else if(foundInputId) {
                                    this.disabledInputsIds.push(foundInputId);
                                    delete this.inputsTypeValuePairs[foundInputId];
                                }
                            });

                            if(errorMessage.length === 0) {
                                errorMessage = `${L('Successful offer recalculation!')}\n\r\n\r${L(limitation.message)}`;
                            } else {
                                errorMessage += `\n\r\n\r${L(limitation.message)}`;
                            }
                        });

                        this.selectedCalculation = newPolicyCalculation;
                        this.toggleMessageBar('policyCalculation', MessageBarType.warning, errorMessage, false);
                    } else if(newPolicyCalculationSet && (!newPolicyCalculation.errors || (newPolicyCalculation.errors && newPolicyCalculation.errors.length === 0))) {
                        this.selectedCalculation = newPolicyCalculation;
                        this.toggleMessageBar('policyCalculation', MessageBarType.success, L('Successful offer recalculation!'), false);
                    } else {
                        this.selectedCalculation = {};
                        this.catchError(newPolicyCalculation.errors[0], "other");
                    }
                }
    
                this.toggleAsyncActionFlag(false);
            }).catch((error) => {
                this.catchError(error, 'policyCalculation');
                this.toggleAsyncActionFlag(false);
            });
        } else {
            this.toggleMessageBar('other', MessageBarType.error, L('Please select offer which you want to recalculate!'), false);
        }
    }

    private async goToInsuranceCompany() {
        if(this.insurancePolicyResponse && Object.keys(this.insurancePolicyResponse).length > 0) {
            const policy = this.insurancePolicyResponse;
            if(policy && policy.insurerName && policy.offerNumber) {
                this.toggleAsyncActionFlag(true);

                await insurancePolicyService.GetPolicyUrl(policy.insurerName, policy.offerNumber).then((response: any) => {
                    if(!response.data.success && response.data.error) {
                        this.catchError(Array.isArray(response.data.error) ? response.data.error[0] : response.data.error, "other");
                    } else if(!!response.data.result) {
                        let link: any = document.createElement("a");
                        link.href = response.data.result;
                        link.target = '_blank';
                        document.body.appendChild(link);
                        link.click();
                        document.body.removeChild(link);
                        link = null;
                    } else {
                        this.toggleMessageBar('other', MessageBarType.error, L('Policy does not have enough data.'), false);
                    }
                }).catch((error: any) => {
                    console.error(catchErrorMessage(error));
                    this.catchError(error, "other");
                });

                this.toggleAsyncActionFlag(false);
            } else {
                this.toggleMessageBar('other', MessageBarType.error, L('Policy does not have enough data.'), false);
            }
        }
    }

    private async downloadPolicyDocuments() {
        if(this.insurancePolicyResponse && Object.keys(this.insurancePolicyResponse).length > 0) {
            const policy = this.insurancePolicyResponse;

            if(policy && this._returnedAbpPolicyId && this._returnedAbpPolicyId > 0) {
                this.toggleAsyncActionFlag(true);

                await insurancePolicyService.DownloadPolicyDocuments(this._returnedAbpPolicyId).then((response: any) => {
                    if(!response.success && response.error) {
                        this.catchError(Array.isArray(response.error) ? response.error[0] : response.error, "other");
                    } else {
                        let link: any = document.createElement("a");
                        link.download = response.result.policyPdfs[0].fileName;
                        link.href = response.result.policyPdfs[0].url;
                        document.body.appendChild(link);
                        link.click();
                        document.body.removeChild(link);
                        link = null;

                        this.toggleMessageBar('documentsGeneratedAndDownloaded', MessageBarType.success, L('Documents generated and downloaded successfully!'), false);
                        this.setState((prevState) => ({ ...prevState, additionalData: { ...this.state.additionalData, policyDocumentsDownloaded: true }, asyncActionInProgress: false }));
                        this.controlledForceUpdate('line 653');
                    }
                }).catch((error: any) => {
                    console.error(catchErrorMessage(error));
                    this.catchError(error, "documentsGeneratedAndDownloaded");
                    this.toggleAsyncActionFlag(false);
                });
            } else {
                this.toggleMessageBar('other', MessageBarType.error, L('Policy does not have enough data.'), false);
            }
        }
    }

    private async generateOcTermination(formData: any) {
        if(formData && this.client && this.client.user && !!this.savedMappedIdsForLaterUse.autoRegistrationNumber) {
            this.toggleAsyncActionFlag(true);

            await ocTerminationService.generateTerminationOcContract({
                "policyId": this._returnedAbpPolicyId,
                "clientFullName": this.client['fullName'] && !!this.client['fullName'] ? this.client['fullName'] : this.client.user.fullName,
                "address": `${this.client.stateProvinceId}, ${this.client.county}, ${this.client.city}, ${this.client.streetAddress} ${this.client.streetAddress2}`,
                "clientId": this.client.id,
                "peselRegon": !!this.client.pesel ? this.client.pesel : this.client.regon,
                "registrationNumber": this.inputsTypeValuePairs[this.savedMappedIdsForLaterUse.autoRegistrationNumber],
                "oldInsurerName": formData.oldInsurerName,
                "oldPolicyNumber": formData.oldPolicyNumber,
                "terminationGround": formData.terminationReason,
                "terminationDate": formData.terminationDate,
                "creationWay": OcTerminationCreationWay.Calculation,
            }).then((response: any) => {
                if(!response.success && response.error) {
                    this.catchError(Array.isArray(response.error) ? response.error[0] : response.error, "other");
                } else {
                    this.toggleMessageBar('ocTerminationGenerated', MessageBarType.success, L('OC termination generated successfully!'), false);
                    this.setState((prevState) => ({ ...prevState, additionalData: { ...this.state.additionalData, ocTerminationGenerated: true }, asyncActionInProgress: false }));
                    this.controlledForceUpdate('line 1662');
                }
            }).catch((error: any) => {
                console.error(catchErrorMessage(error));
                this.catchError(error, "ocTerminationGenerated");
                this.toggleAsyncActionFlag(false);
            });
        } else {
            this.toggleMessageBar('other', MessageBarType.error, L('Policy does not have enough data.'), false);
        }
    }

    private async sendPolicyMail() {
        if(this._returnedAbpPolicyId && this._returnedAbpPolicyId > 0) {
            this.toggleAsyncActionFlag(true);

            await clientAttachedFilesService.sendPolicyMail(this._returnedAbpPolicyId).then((response: any) => {
                if(!response.success && response.error) {
                    this.catchError(Array.isArray(response.error) ? response.error[0] : response.error, "other");
                } else {
                    this.toggleMessageBar('sendPolicyMail', MessageBarType.success, L('Policy mail sent successfully!'), false);
                    this.setState((prevState) => ({ ...prevState, additionalData: { ...this.state.additionalData, policyMailSended: true }, asyncActionInProgress: false }));
                    this.controlledForceUpdate('line 1685');
                }
            }).catch((error: any) => {
                console.error(catchErrorMessage(error));
                this.catchError(error, "sendPolicyMail");
                this.toggleAsyncActionFlag(false);
            });
        }
    }

    private async getSingleProduct(selectedProduct: string | number | undefined) {
        if(!selectedProduct || selectedProduct === this.selectedProduct) return;

        if(validateLocalStorageKeyAndTimestamp(`policyCalculationProduct${selectedProduct}`, hourInMs * 12, true)) {
            this.product = JSON.parse(sessionStorage.getItem(`policyCalculationProduct${selectedProduct}`)!).data;
            this.selectedProduct = selectedProduct;
            this.forceUpdate();
        } else {
            this.toggleAsyncActionFlag(true);
            this.changeLoadSpinnerLabel(L('Downloading product data.'));

            await this.props.productStore?.getProductWithMappingsForProductId(selectedProduct.toString()).then((productResult: any) => {
                this.product = productResult;

                saveInStorage(`policyCalculationProduct${selectedProduct}`, {data: this.product, timestamp: new Date().getTime()}, true);
                this.controlledForceUpdate('line 667');
            });

            this.selectedProduct = selectedProduct;
            this.toggleAsyncActionFlag(false, true);
        }
    }

    private async getVehicleConfigByVin(vin: string) {
        this.asyncActionInProgress = true;
        this.forceUpdate();

        await vehicleConfigService.getByVin(vin).then((response: any) => {
            if(response && response.id > 0) {
                this.eurotaxInfoexpertFormData = response;
                this.customInputsData['vehicleTypeId'] = this.eurotaxInfoexpertFormData.type;
                this.customInputsData['vehicleBrandId'] = this.eurotaxInfoexpertFormData.brand;
                this.customInputsData['productionYear'] = this.eurotaxInfoexpertFormData.year;
                this.customInputsData['fuelType'] = this.eurotaxInfoexpertFormData.fuelType;
                this.customInputsData['vehicleModelId'] = this.eurotaxInfoexpertFormData.model;
                this.customInputsData['enginePower'] = parseInt(this.eurotaxInfoexpertFormData.enginePower);
                this.customInputsData['engineCapacity'] = parseInt(this.eurotaxInfoexpertFormData.engineCapacity);
                this.customInputsData['vehicleConfigurationEurotaxId'] = this.eurotaxInfoexpertFormData.eurotax;
                this.customInputsData['vehicleConfigurationInfoExpertId'] = this.eurotaxInfoexpertFormData.infoExpert;
                this.inputsTypeValuePairs[this.savedMappedIdsForLaterUse.autoProductionYear] = !!this.eurotaxInfoexpertFormData.year ? parseInt(this.eurotaxInfoexpertFormData.year) : this.inputsTypeValuePairs[this.savedMappedIdsForLaterUse.autoProductionYear];
                this.inputsTypeValuePairs[this.savedMappedIdsForLaterUse.autoMileage] = !!this.eurotaxInfoexpertFormData.mileage ? this.eurotaxInfoexpertFormData.mileage : this.inputsTypeValuePairs[this.savedMappedIdsForLaterUse.autoMileage];
                this.inputsTypeValuePairs[this.savedMappedIdsForLaterUse.autoFirstRegistrationDate] = !!this.eurotaxInfoexpertFormData.firstRegistrationDate && this.eurotaxInfoexpertFormData.firstRegistrationDate.substring(0, 4) !== '0001' ? 
                                                                                                        this.eurotaxInfoexpertFormData.firstRegistrationDate : this.inputsTypeValuePairs[this.savedMappedIdsForLaterUse.autoFirstRegistrationDate];
                this.inputsTypeValuePairs[this.savedMappedIdsForLaterUse.autoRegistrationNumber] = !!this.eurotaxInfoexpertFormData.registrationNumber ? this.eurotaxInfoexpertFormData.registrationNumber : this.inputsTypeValuePairs[this.savedMappedIdsForLaterUse.autoRegistrationNumber];
            }
        }).catch((error: any) => {
            console.error(error);
        });

        this.asyncActionInProgress = false;
        this.forceUpdate();
    }

    private async apiCall(step: number) {
        this.toggleAsyncActionFlag(true, true);

        switch(step) {
            case 1:
                // let isThisOriginProduct: boolean = (this.prevProductId && this.prevProductId?.length > 0 ? false : true);

                // if(this.product?.Id !== this.selectedProduct) {
                //     await this.props.productStore?.get({ id: this.selectedProduct } as ProductDto).then((productResult) => {
                //         this.product = productResult;
                //         this.clearDataOnProductChange();
    
                //         if(isThisOriginProduct && this.isEditMode) {
                //             this.inputsTypeValuePairs = {...this.inputsTypeValuePairs, ...this.tempInputsTypeValuePairsFromEdit};
                //         }
    
                //         this.controlledForceUpdate('line 697');
                //     });
                // }

                if(!this.productAttributes || Object.keys(this.productAttributes).length <= 0) {
                    await this.props.productAttributeStore!.getAll(this.props.productAttributeStore!.defaultRequest);
                    this.productAttributes = this.props.productAttributeStore && this.props.productAttributeStore.dataSet && this.props.productAttributeStore.dataSet.items ? this.props.productAttributeStore.dataSet.items : {};
                    this.isDataLoaded = true;
                }

                if(!this.clientDataFilled && (!this.client || parseInt(this.client.id) <= 0)) {
                    this.client = await this.props.clientStore?.get({...defaultClient, id: this.selectedClient});
                    if(this.client && !!this.client.id) {
                        this.clientDataFilled = true;
                    }
                }

                if(this.autoCalculationOwner === AutoCalculationOwnerType.Insurer) {
                    this.cloneProductAttributeMappingsData(
                        this.savedProductAttributeMappings.productAttributeMappingsToChange,
                        this.savedProductAttributeMappings.productAttributeMappingsToWatch,
                    );
                }

                if(this.selectedProduct === this.mapKeyToId("mapProductNameToProductId", "ubezpieczenie-auta")) {
                    this.changeLoadSpinnerLabel(L('Downloading user vehicles.'));
                    await this.props.vehicleStore?.getByClientId(this.selectedClient);
                    this.changeLoadSpinnerLabel(null);
                }

                break;
            case 2:
                if(!this.preSettedApkData || this.preSettedApkData.length === 0) {
                    this.setApkData();
                }

                // if(this.selectedProduct === this.mapKeyToId("mapProductNameToProductId", "ubezpieczenie-auta")) {
                //     await vehicleConfigService.saveByVin({...this.eurotaxInfoexpertFormData, clientId: this.client?.id}).then((response: any) => {}).catch((error: any) => {
                //         this.catchError(error, "other");
                //     });
                // }
                
                this.lastGetCalculationsPayload = buildRequestBody('getCalculations', this, this.getProductTypeByProduct(this.product), this.getMapNameByProduct(this.product));
                await policyCalculationService.getCalculations(this.lastGetCalculationsPayload).then((response) => {
                    this.calculations = {...response};
                    this.getCalculationsRawResponse = {...response.data.result};
                }).catch((error) => {
                    this.catchError(error, "other");
                });
                
                break;
            case 3:
                if(!this.preSettedApkData || this.preSettedApkData.length === 0) {
                    this.setApkData();
                }

                const helperTravelersTableKeysLength: number = this.inputsTypeValuePairs[this.savedMappedIdsForLaterUse.travelersTableId] ? 
                                                                Object.keys(JSON.parse(this.inputsTypeValuePairs[this.savedMappedIdsForLaterUse.travelersTableId])).length : 0;
                const helperTravelersTableFullDataKeysLength: number = this.inputsTypeValuePairs[this.savedMappedIdsForLaterUse.travelersTableFullDataId] ? 
                                                                Object.keys(JSON.parse(this.inputsTypeValuePairs[this.savedMappedIdsForLaterUse.travelersTableFullDataId])).length : 0;
                const helperCancelTravelTravelersTableKeysLength: number = this.inputsTypeValuePairs[this.savedMappedIdsForLaterUse.cancelTravelersTableId] ? 
                                                                Object.keys(JSON.parse(this.inputsTypeValuePairs[this.savedMappedIdsForLaterUse.cancelTravelersTableId])).length : 0;

                const inputsDataForAgreements: any = {
                    'OfferNumber': this.selectedCalculation && !!this.selectedCalculation.offerNumber ? this.selectedCalculation.offerNumber : '',
                    // 'CalculationId': this.selectedCalculation && isCalculationIdSet(this.selectedCalculation.calculationId) ? 
                    //                     (typeof this.selectedCalculation.calculationId === 'number' ? 
                    //                         this.selectedCalculation.calculationId.toString() : this.selectedCalculation.calculationId) : '',
                    'InputVehicleOwner': this.autoCalculationOwner,
                    'InputVehicleHasCoowner': this.vehicleHasCoowner,
                    'InputTravelMoreThanOneInsured': helperTravelersTableKeysLength > 1 || helperTravelersTableFullDataKeysLength > 1 || helperCancelTravelTravelersTableKeysLength > 1
                                                        ? true : false,
                    'InputNNWOtherInsurer': this.insurerIsInsured,
                };

                await agreementsService.getAgreements(
                    this.selectedCalculation.insurerName, 
                    this.getCalculationsRawResponse.calculationId,
                    this.getProductTypeByProduct(this.product), this.isFastCalculation ? 'fast_calculation' : 'standard',
                    inputsDataForAgreements
                ).then((response: any) => {
                    if(response && Array.isArray(response) && response.length > 0) {
                        this.selectedOfferAgreements = response;
                    }
                }).catch((error: any) => {
                    console.error(error);
                });
                break;
            case 4:
                if(typeof this.selectedProduct !== 'string' || this.selectedProduct.length <= 0) {
                    this.setState((prevState) => ({ ...prevState, message: { text: L('Invalid product!'), type: MessageBarType.error } }));
                } else if(typeof this.selectedCalculation === 'undefined' || this.selectedCalculation!.gnInsurerId.length === 0) {
                    this.setState((prevState) => ({ ...prevState, message: { text: L('Invalid calculation!'), type: MessageBarType.error } }));
                } else {
                    if(this.client && !this.orderCreated) {
                        if(this.isEditMode && this.props.policyCalculation?.payloadType === "order") {
                            await orderService.update(buildRequestBody('updateOrder', this, this.getProductTypeByProduct(this.product), this.getMapNameByProduct(this.product), this.keysToIdMapper)).then((response) => {
                                this.orderCreated = true;
                                this._returnedCreatedOrder = response;
    
                                this.toggleMessageBar('createOrder', MessageBarType.success, L('Order updated successfully!'), false);
                            }).catch((error) => {
                                this.catchError(error, "createOrder");
                            });
                        } else {
                            await orderService.create(buildRequestBody('createOrder', this, this.getProductTypeByProduct(this.product), this.getMapNameByProduct(this.product))).then((response) => {
                                this.orderCreated = true;
                                this._returnedCreatedOrder = response;
                                this.toggleMessageBar('createOrder', MessageBarType.success, L('Order saved successfully!'), false);
                            }).catch((error) => {
                                this.catchError(error, "createOrder");
                            });
                        }
                    } else if(this.client && this.orderCreated && !this.orderSent) {
                        await policyApplicationService.applicate(buildRequestBody('policyApplication', this, this.getProductTypeByProduct(this.product), this.getMapNameByProduct(this.product))).then((response) => {
                            if(response && response.data && response.data.result && response.data.result.policy) {
                                let policy = response.data.result.policy;
                                
                                if(policy.success) {
                                    // policy['clientInspectionLink'] = "https://google.com";
                                    // policy['agentInspectionLink'] = "https://google2.com";
                                    this.insurancePolicyResponse = policy;
                                    this._returnedAbpPolicyId = response.data.result.abpPolicyId;
                                    this.orderSent = true;
                                    this.toggleMessageBar('policyApplication', MessageBarType.success, L('Calculation sent successfully!'), false);
                                } else if(policy.errors && policy.errors.length > 0) {
                                    let text = "";
                    
                                    policy.errors.forEach((error: string) => {
                                        text += error;
                                        if(policy.errors.length > 1) {
                                            text += "\n\r\n\r";
                                        }
                                    });
                    
                                    this.toggleMessageBar('policyApplication', MessageBarType.error, text, false);
                                } else {
                                    this.toggleMessageBar('policyApplication', MessageBarType.error, L('Not enough data available.'), false);
                                }
                            } else {
                                this.toggleMessageBar('policyApplication', MessageBarType.error, L('Response from server is not valid.'), false);
                            }
                        }).catch((error) => {
                            this.catchError(error, "policyApplication");
                        });
                    } else if(this.client && this.orderCreated && this.orderSent && (!!this.insurancePolicyResponse.clientInspectionLink && !this.inspectionLinkSent)) {
                        let mobilePhone: string = "";
                        for(let key in this.clientTypeValuePairs.insurer) {
                            if(this.clientTypeValuePairs.insurer.hasOwnProperty(key) && mobilePhone.length === 0) {
                                if(key.includes('MobilePhone')) {
                                    mobilePhone = this.clientTypeValuePairs.insurer[key];
                                }
                            }
                        }

                        await smsService.sendSms(mobilePhone, this.insurancePolicyResponse.clientInspectionLink).then((response: any) => {
                            this.inspectionLinkSent = true;
                            if(response && response.success) {
                                this.toggleMessageBar('sendSms', MessageBarType.success, L('SMS sent successfully!'), false);
                            } else {
                                this.toggleMessageBar('sendSms', MessageBarType.error, L('An error occurred while trying to send the SMS!'), false);
                            }
                        }).catch((error: any) => {
                            this.catchError(error, "sendSms");
                            this.inspectionLinkSent = true;
                        });
                    } else if(this.client && this.orderCreated && this.orderSent && (this.inspectionLinkSent || !this.insurancePolicyResponse.clientInspectionLink) && !this.policyFinalized) {
                        if(this.selectedCalculation.insurerName === 'Allianz' || !!this.uwAcceptanceCode) {
                            await policyFinalizationService.finalizeWithAcceptanceUw(buildRequestBody('policyFinalizationWithAcceptanceUw', this, this.getProductTypeByProduct(this.product), this.getMapNameByProduct(this.product))).then(async (response) => {
                                if(response && response.data && response.data.result && response.data.result.policy) {
                                    let policy = response.data.result.policy;
                                    
                                    if(policy.success) {
                                        this.policyFinalized = true;
                                        this.toggleMessageBar('policyFinalization', MessageBarType.success, L('Policy finalization success!'), false);
                                        
                                        let confirmPaymentDto: ConfirmPaymentDto = {
                                            policyId: this._returnedAbpPolicyId,
                                            fee: policy.price,
                                            installmentNumber: 1
                                        };

                                        await paymentService.confirmPayment(confirmPaymentDto).then((response: any) => {}).catch((error: any) => {
                                            this.catchError(error, "confirmPayment");
                                        });
                                    } else if(policy.errors && policy.errors.length > 0) {
                                        let text = "";
                        
                                        policy.errors.forEach((error: string) => {
                                            text += error;
                                            if(policy.errors.length > 1) {
                                                text += "\n\r\n\r";
                                            }
                                        });
                        
                                        this.toggleMessageBar('policyFinalization', MessageBarType.error, text, false);
                                    } else {
                                        this.toggleMessageBar('policyFinalization', MessageBarType.error, L('Not enough data available.'), false);
                                    }
                                } else {
                                    this.toggleMessageBar('policyFinalization', MessageBarType.error, L('Response from server is not valid.'), false);
                                }
                            }).catch((error) => {
                                this.catchError(error, "policyFinalization");
                            });
                        } else {
                            await policyFinalizationService.finalize(buildRequestBody('policyFinalization', this, this.getProductTypeByProduct(this.product), this.getMapNameByProduct(this.product))).then((response) => {
                                if(response && response.data && response.data.result && response.data.result.policy) {
                                    let policy = response.data.result.policy;
                                    if(policy.success) {
                                        this.policyFinalized = true;
                                        this.toggleMessageBar('policyFinalization', MessageBarType.success, L('Policy finalization success!'), false);
                                    } else if(policy.errors && policy.errors.length > 0) {
                                        let text = "";
                        
                                        policy.errors.forEach((error: string) => {
                                            text += error;
                                            if(policy.errors.length > 1) {
                                                text += "\n\r\n\r";
                                            }
                                        });
                        
                                        this.toggleMessageBar('policyFinalization', MessageBarType.error, text, false);
                                    } else {
                                        this.toggleMessageBar('policyFinalization', MessageBarType.error, L('Not enough data available.'), false);
                                    }
                                } else {
                                    this.toggleMessageBar('policyFinalization', MessageBarType.error, L('Response from server is not valid.'), false);
                                }
                            }).catch((error) => {
                                this.catchError(error, "policyFinalization");
                            });
                        }
                    } else {
                        this.setState((prevState) => ({ ...prevState, message: { text: L('Customer not found!'), type: MessageBarType.error } }));
                    }
                }
                break;
        }
        
        this.toggleAsyncActionFlag(false);
        if(this.step < AppConfig.calculationAllSteps) {
            this.step++;
        }

        this.controlledForceUpdate('line 841');
    }

    private nextStep = () => {
        // if(this.customerDataFilled && this.step === 1 && !this.dialogConfirmed && this.selectedCustomer) {
        //     this.showConfirmationDialog = true;
        //     this.controlledForceUpdate('line 850');
        // } else 

        if(this.step === 1) {
            this.adjustInputsChangedManually = {};

            for(let key in this.templateInputsForCalculationAdjust) {
                if(this.templateInputsForCalculationAdjust.hasOwnProperty(key)) {
                    delete this.inputsTypeValuePairs[key];
                }
            }
        }

        if(this.step === 3) {
            this.setState((prevState) => ({ ...prevState, disableRenderMessage: true }));
        }

        if(this.inputErrors === 0) {
            this.apiCall(this.step);
        }
    }

    private prevStep = () => {
        if(this.step === 3 && !this.goBackToStep2Confirmed) {
            const alreadyPushedLabels: string[] = [];
            this.templateInputsForCalculationAdjustList = [];
            for(let key in this.templateInputsForCalculationAdjust) {
                if(this.templateInputsForCalculationAdjust.hasOwnProperty(key) && !alreadyPushedLabels.includes(this.templateInputsForCalculationAdjust[key].property.label)) {
                    alreadyPushedLabels.push(this.templateInputsForCalculationAdjust[key].property.label);
                    this.templateInputsForCalculationAdjustList.push(<li>{`${this.templateInputsForCalculationAdjust[key].property.label}`}</li>);
                }
            }
            this.preSettedApkData = [];
            this.showPrevStepConfirmationDialog = true;
            this.controlledForceUpdate('line 1770');
            return;
        } else if (this.step === 3 && this.goBackToStep2Confirmed) {
            this.goBackToStep2Confirmed = false;
        }

        if(this.step === 3 || this.step === 2) {
            this.adjustInputsChangedManually = {};

            for(let key in this.templateInputsForCalculationAdjust) {
                if(this.templateInputsForCalculationAdjust.hasOwnProperty(key)) {
                    delete this.inputsTypeValuePairs[key];
                }
            }
        }

        if(this.step === 2 && this.blockNextStepButton === true) {
            this.blockNextStepButton = false;
        }

        if(this.step === 4) {
            this.setState((prevState) => ({ ...prevState, disableRenderMessage: false }));
        }

        if(this.step > 1) {
            this.step--;
        }

        // if(this.step === 1) {
        //     this.dialogConfirmed = false;
        // }

        if(this.step === 1 || this.step === 2) {
            this.selectedCalculation = undefined;
            this.inputsChangedManually = {};
        }

        this.controlledForceUpdate('line 1800');
    }

    private dialogPrevStepOnAction(confirm: boolean) {
        this.goBackToStep2Confirmed = confirm;
        this.showPrevStepConfirmationDialog = false;

        if(confirm) {
            this.prevStep();
        } else {
            this.controlledForceUpdate('line 1810');
        }
    }

    // private dialogOnAction(confirm: boolean) {
    //     this.showConfirmationDialog = false;
    //     this.dialogConfirmed = true;
    //     this.clientDataFilled = !confirm;

    //     this.nextStep();
    // }

    private toggleAsyncActionFlag(newState: boolean, forceUpdate?: boolean) {
        if(typeof newState === 'boolean') {
            this.setState((prevState) => ({ ...prevState, asyncActionInProgress: newState }));
            
            if(forceUpdate) {
                this.controlledForceUpdate('line 894');
            }
        }
    }

    private handleOnInputChange(id: string, value: any, userFields?: any, stepNumber?: number) {
        if(stepNumber === 1) {
            this.catchDataForCustomerInputs(id, value, userFields);
            this.catchDataForCustomInputs(id, value);
            this.inputsTypeValuePairs[id] = value; 
            if(!!userFields) {
                this.inputsIdUserFieldsPairs[id] = userFields;
            }; 
        } else if(stepNumber === 2) {
            this.catchDataForCustomInputs(id, value);
            this.inputsTypeValuePairs[id] = value;
            if(!!userFields) {
                this.inputsIdUserFieldsPairs[id] = userFields;
            }; 

            if(this.setDelayedInputNewValue && this.setDelayedInputNewValue[id]) {
                this.inputsTypeValuePairs[id] = this.setDelayedInputNewValue[id];
                this.setDelayedInputNewValue = {};
            }
            this.inputValuePairsStringified = JSON.stringify(this.inputsTypeValuePairs); 
        } else if(stepNumber === 3) {
            this.inputsTypeValuePairs[id] = value;
            if(!!userFields) {
                this.inputsIdUserFieldsPairs[id] = userFields;
            }; 
        }
        this.controlledForceUpdate('line 1457');
    }
    
    handleChildTabChange = (selectedKey: string) => {
        this.tabsSelectedKey = selectedKey;
        this.forceUpdate();
    }

    renderConfirm = () => {
        if(this.step === 1 || this.step === 2 || this.step === 3) {
            const buttonText: string = this.step === 2 ? L('Make a calculation') : (this.step === 3 ? L('Summary') : L('Next step'));
            const buttonIcon: string = this.step === 2 ? 'Accept' : 'ChromeBackMirrored';

            return <div className={classNames.confrimButtonWrapper}>
                { (this.step === 3 && (typeof this.selectedCalculation === 'undefined' || !this.selectedCalculation.gnInsurerId)) ? 
                    <DefaultButton theme={myTheme} onClick={this.nextStep} text={buttonText} iconProps={{ iconName: buttonIcon, color: myTheme.palette.white }} 
                        disabled={typeof this.selectedCalculation === 'undefined' || !this.selectedCalculation.gnInsurerId || !this.selectedProduct || this.state.asyncActionInProgress || this.inputErrors > 0 || this.blockNextStepButton || this.state.blockNextStepButton}
                    />
                    :
                    <PrimaryButton theme={myTheme} onClick={this.nextStep} text={buttonText} iconProps={{ iconName: buttonIcon, color: myTheme.palette.white }} 
                        disabled={!this.selectedProduct || this.state.asyncActionInProgress || this.inputErrors > 0 || this.blockNextStepButton || this.state.blockNextStepButton}
                    />
                }
                
                {this.state.asyncActionInProgress ? <Spinner label={typeof this.loadSpinnerCustomLabel === 'string' ? this.loadSpinnerCustomLabel : L('Please wait...')}
                    className={classNames.loadSpinner} size={SpinnerSize.large} ariaLive="assertive" labelPosition="right" /> : ''}
            </div>;
        } else {
            return <></>;
        }
    };

    renderBack = () => {
        let buttons = <DefaultButton text={L('Close calculation')} iconProps={{ iconName: 'Cancel' }} onClick={this._onBack} allowDisabledFocus />;
        
        if(this.step > 1 && !this.orderCreated) {
            buttons = <>
                <DefaultButton text={L('Close calculation')} iconProps={{ iconName: 'Cancel' }} onClick={this._onBack} allowDisabledFocus />
                <DefaultButton text={L('Previous step')} iconProps={{ iconName: 'Back' }} onClick={this.prevStep} allowDisabledFocus disabled={this.state.asyncActionInProgress} />
                {this.step === 2 && this.childTabSwitch && parseInt(this.tabsSelectedKey) !== this.tabs.length - 1 &&
                    <DefaultButton text={L('Next step')} iconProps={{iconName: 'ChromeBackMirrored'}} onClick={this.childTabSwitch} disabled={this.tabsSelectedKey === "4"} />
                }
            </>;
        }

        return buttons;
    };

    renderContent() {
        return <>
            {/* <Dialog
                hidden={!this.showConfirmationDialog}
                onDismiss={() => this.dialogOnAction(false)}
                dialogContentProps={{
                    type: DialogType.normal,
                    title: L('Confirmation required'),
                    subText: L('Do you want to overwrite customer details?'),
                }}
                modalProps={{
                    isBlocking: true
                }}
            >
                <DialogFooter>
                    <PrimaryButton onClick={() => this.dialogOnAction(true)} text={L('Yes')} />
                    <DefaultButton onClick={() => this.dialogOnAction(false)} text={L('No')} />
                </DialogFooter>
            </Dialog> */}

            <Dialog
                hidden={!this.showPrevStepConfirmationDialog}
                onDismiss={() => this.dialogPrevStepOnAction(false)}
                dialogContentProps={{
                    type: DialogType.normal,
                    title: L('Confirmation required'),
                    subText: L('If you adjusted the offer of a specific Insurer on this screen, it will be restored to the base values after the calculation. Added discount codes removed.'),
                }}
                modalProps={{
                    isBlocking: true
                }}
            >
                <DialogFooter>
                    <PrimaryButton onClick={() => this.dialogPrevStepOnAction(true)} text={L('Yes')} />
                    <DefaultButton onClick={() => this.dialogPrevStepOnAction(false)} text={L('No')} />
                </DialogFooter>
            </Dialog>
            
            <h2 className={classNames.pageTitle}>{L("Client")}</h2>
            <StepsLayer step={this.step} hostId="calculation-steps-layer-host" />

            <LayerHost id="calculation-steps-layer-host" className={classNames.layerHost}>
                {this.step === 2 ?
                    <PolicyCalculationStep2 isDataLoaded={this.isDataLoaded} customInputsData={this.customInputsData} savedMappedIdsForLaterUse={this.savedMappedIdsForLaterUse}
                        product={this.product} inputsTypeValuePairs={this.inputsTypeValuePairs} gnLanguage={this.gnLanguage} countryStore={this.props.countryStore}
                        travelCountryStore={this.props.travelCountryStore} sportDisciplineStore={this.props.sportDisciplineStore} productAttributes={this.productAttributes}
                        onInputChange={(id: string, value: any, userFields?: any) => this.handleOnInputChange(id, value, userFields, this.step)}
                        mapKeyToId={(mapType: string, key: string) => this.mapKeyToId(mapType, key)} inputValuePairsStringified={this.inputValuePairsStringified}
                        onMassInputChange={(inputFields: any, userFields: any) => { this.inputsTypeValuePairs = {...this.inputsTypeValuePairs, ...inputFields}; this.inputValuePairsStringified = JSON.stringify(this.inputsTypeValuePairs); this.inputsIdUserFieldsPairs = {...this.inputsIdUserFieldsPairs, ...userFields}; this.loopInputsTypeValuePairs(); }}
                        toggleAsyncActionFlag={(newState: boolean, forceUpdate: boolean) => this.toggleAsyncActionFlag(newState, forceUpdate) } inputsIdUserFieldsPairs={this.inputsIdUserFieldsPairs}
                        saveInputsForCalculationAdjust={(inputs: any) => { this.saveInputsForCalculationAdjust(inputs); }}
                        isFastCalculation={this.isFastCalculation} setIsFastCalculation={(value: boolean) => { this.isFastCalculation = value; this.controlledForceUpdate('line 958'); }}
                        setInputsUserFields={(id: string, userFields: any) => { this.inputsIdUserFieldsPairs[id] = userFields; this.controlledForceUpdate(); }} 
                        setInputErrors={(errorsCount: number) => {
                            this.inputErrors = errorsCount; 
                            if(errorsCount > 0) {
                                this.catchError(L(""), "other", false); // Correct errors in the form and/or fill in the required fields.
                            } else { 
                                this.catchError("", "other", true); 
                            } 
                            this.controlledForceUpdate('line 1989');
                        }}
                        inputErrors={this.inputErrors} allUserFields={this.allUserFields}
                        changeAdjustInputsChangedManually={(id: string, value: any) => { this.adjustInputsChangedManually[id] = value; this.controlledForceUpdate(); }}
                        inputsChangedManually={this.inputsChangedManually} asyncActionInProgress={this.state.asyncActionInProgress}
                        isEditMode={this.isEditMode} vehicleStore={this.props.vehicleStore} tempSelectedVehicle={this.tempSelectedVehicle}
                        onTempVehicleSelect={(tempVehicle: any, justId: boolean = false) => {
                            if(!!justId && justId === true) {
                                this.tempSelectedVehicle = typeof tempVehicle === 'number' ? tempVehicle.toString() : tempVehicle;
                            } else {
                                this.tempSelectedVehicle = tempVehicle && typeof tempVehicle !== 'undefined' ? typeof tempVehicle.id === 'number' ? tempVehicle.id.toString() : tempVehicle.id : ""; this.controlledForceUpdate();
                            }
                        }}
                        customPresettedInputOptions={this.customPresettedInputOptions}
                        formTabSwitch={this.formChildTabSwitch}
                        onTabChange={this.handleChildTabChange} inputErrorsText={this.inputErrorsText}
                        setEurotaxInfoexpertFormData={(id: string, value: string) => { this.eurotaxInfoexpertFormData[id] = value; this.forceUpdate(); }}
                        savedTemplateInputsForTable={this.savedTemplateInputsForTable}
                        setSavedTemplateInputsForTable={(value: any) => { this.savedTemplateInputsForTable = value; this.forceUpdate(); }}
                        hiddenInputsTypeValuePairs={this.hiddenInputsTypeValuePairs}
                        setHiddenInputsTypeValuePairs={(id: string, value: any) => { 
                            if(!!value) { this.hiddenInputsTypeValuePairs[id] = value; } else { delete this.hiddenInputsTypeValuePairs[id]; }
                            this.forceUpdate(); 
                        }}
                        changeLoadSpinnerLabel={(newLabel: string | null) => this.changeLoadSpinnerLabel(typeof newLabel === 'string' ? L(newLabel) : newLabel)}
                    />
                    :
                    <Pivot className={classNames.toolbar} theme={myTheme} styles={pivotStyles}>
                        <PivotItem key={this.step}>
                            {this.step === 1 ? 
                                <PolicyCalculationStep1 allProducts={this.allProducts} productAttributes={this.productAttributes}
                                    clientStore={this.props.clientStore} clientLeasingStore={this.props.clientLeasingStore} selectedClientData={this.selectedClientData}
                                    selectedProduct={this.selectedProduct} selectedClient={this.selectedClient} tempSelectedClient={this.tempSelectedClient} 
                                    asyncActionInProgress={this.state.asyncActionInProgress} blockNextStepButton={this.blockNextStepButton}
                                    onProductSelect={(productId: string | number | undefined) => {
                                        this.getSingleProduct(productId); this.controlledForceUpdate('line 970');
                                    }}
                                    userCrudStore={this.props.userCrudStore} history={this.props.history}
                                    mapKeyToId={(mapType: string, key: string) => this.mapKeyToId(mapType, key)}
                                    allUserFields={this.allUserFields} countryStore={this.props.countryStore}
                                    onMassInputChange={(inputFields: any, userFields: any) => { this.inputsTypeValuePairs = {...this.inputsTypeValuePairs, ...inputFields}; this.inputsIdUserFieldsPairs = {...this.inputsIdUserFieldsPairs, ...userFields}; this.loopInputsTypeValuePairs(); }} 
                                    onFillFormWithSelectedClientData={(productAttributeMappingsToChange: any[]) => this.fillFormWithSelectedClientData(productAttributeMappingsToChange)}
                                    onInputChange={(id: string, value: any, userFields?: any) => this.handleOnInputChange(id, value, userFields, this.step)}
                                    product={this.product} isDataLoaded={this.isDataLoaded} inputErrors={this.inputErrors}
                                    inputsTypeValuePairs={this.inputsTypeValuePairs} gnLanguage={this.gnLanguage}
                                    inputsIdUserFieldsPairs={this.inputsIdUserFieldsPairs} customerTypeValuePairs={this.clientTypeValuePairs} 
                                    setInputErrors={(errorsCount: number) => { 
                                        this.inputErrors = errorsCount; if(errorsCount > 0) { this.catchError(L("Correct errors in the form and/or fill in the required fields."), "other", false, MessageBarType.warning); } else if(this.blockNextStepButton === false) { this.catchError("", "other", true); } this.controlledForceUpdate(); }} 
                                    setBlockNextStepButton={(value: boolean, message?: string) => {
                                        this.blockNextStepButton = value; if(value === true) { this.catchError(message, "other", false, MessageBarType.warning); } else if(this.inputErrors === 0) { this.catchError("", "other", true); } this.controlledForceUpdate(); }}
                                    onCustomerSelect={(customer: any, justId: boolean = false) => {
                                        if(!!justId && justId === true) {
                                            this.selectedClient = typeof customer === 'number' ? customer.toString() : customer;
                                        } else {
                                            this.selectedClient = customer && typeof customer !== 'undefined' ? typeof customer.id === 'number' ? customer.id.toString() : customer.id : ""; this.controlledForceUpdate();
                                        }
                                    }}
                                    onTempCustomerSelect={(tempCustomer: any, justId: boolean = false) => {
                                        if(!!justId && justId === true) {
                                            this.tempSelectedClient = typeof tempCustomer === 'number' ? tempCustomer.toString() : tempCustomer;
                                        } else {
                                            this.tempSelectedClient = tempCustomer && typeof tempCustomer !== 'undefined' ? typeof tempCustomer.id === 'number' ? tempCustomer.id.toString() : tempCustomer.id : ""; this.controlledForceUpdate();
                                        }
                                    }} onCloneProductAttributeMappingsData={(productAttributeMappingsToChange: any[], productAttributeMappingsToClone: any[], unsetAllValues?: boolean) => this.cloneProductAttributeMappingsData(productAttributeMappingsToChange, productAttributeMappingsToClone, unsetAllValues)}
                                    autoCalculationOwner={this.autoCalculationOwner} onAutoCalculationOwnerChange={(value: string) => { this.autoCalculationOwner = value; this.controlledForceUpdate(); } }
                                    toggleAsyncActionInProgress={(bool: boolean) => { this.toggleAsyncActionFlag(bool); }}
                                    toggleVehicleCoowner={(coownerAttributes: any[]) => {
                                        if(coownerAttributes && Array.isArray(coownerAttributes) && coownerAttributes.length > 1) {
                                            this.vehicleHasCoowner = true;
                                        } else {
                                            this.vehicleHasCoowner = false;
                                        }
                                    }}
                                    isEditMode={this.isEditMode} forcedLastChangedSection={this.forcedLastChangedSection} loadSpinnerCustomLabel={this.loadSpinnerCustomLabel}
                                    toggleForcedLastChangedSection={(value?: string) => { this.forcedLastChangedSection = !!value ? value : undefined; this.controlledForceUpdate(); }}
                                /> : <></>}
                            {this.step === 3 ?
                                <PolicyCalculationStep3 calculations={this.calculations} selectedCalculation={this.selectedCalculation} templateInputsForCalculationAdjust={this.templateInputsForCalculationAdjust}
                                    onCalculationSelect={(calculation: any) => { if(this.selectedCalculation && calculation.gnInsurerId === this.selectedCalculation.gnInsurerId) { this.selectedCalculation = undefined; this.controlledForceUpdate(); } else if(!this.selectedCalculation || (this.selectedCalculation && calculation.gnInsurerId !== this.selectedCalculation.gnInsurerId)) { this.selectedCalculation = calculation; this.controlledForceUpdate(); }}}
                                    setFilteredCalculations={(filteredCalculations: any) => { this.filteredCalculations = filteredCalculations; }} inputsTypeValuePairs={this.inputsTypeValuePairs}
                                    onInputChange={(id: string, value: any, userFields?: any) => this.handleOnInputChange(id, value, userFields, this.step)} gnLanguage={this.gnLanguage} 
                                    getSingleCalculation={(insurerName: string) => this.getSingleCalculation(insurerName) } messageBoxData={this.summaryMessageBoxData.policyCalculation}
                                    setInputsUserFields={(id: string, userFields: any) => { this.inputsIdUserFieldsPairs[id] = userFields; this.controlledForceUpdate(); }} 
                                    inputsIdUserFieldsPairs={this.inputsIdUserFieldsPairs} getCalculationsRawResponse={this.getCalculationsRawResponse} product={this.product}
                                    toggleAsyncActionInProgress={(bool: boolean) => { this.toggleAsyncActionFlag(bool); }}
                                    adjustInputsChangedManually={this.adjustInputsChangedManually} lastGetCalculationsPayload={this.lastGetCalculationsPayload} selectedClient={this.selectedClient}
                                    lastGetCalculationPayload={this.lastGetCalculationPayload} mapKeyToId={(mapType: string, key: string) => this.mapKeyToId(mapType, key)}
                                    catchError={ (error: any, callType: string, hide?: boolean, type?: MessageBarType) => this.catchError(error, callType, hide, type) }
                                    getMapNameByProduct={ (product: any) => this.getMapNameByProduct(product)} preSettedApkData={this.preSettedApkData } productAttributes={this.productAttributes}
                                    triggerSetApkData={ () => this.setApkData() } disabledInputsData={{inputsIds: this.disabledInputsIds, optionsIds: this.disabledInputOptionsIds}}
                                    productType={this.getProductTypeByProduct(this.product)} customInputsData={this.customInputsData} asyncActionInProgress={this.state.asyncActionInProgress}
                                /> : <></>}
                            {this.step === 4 ?
                                <PolicyCalculationStep4 selectedCalculation={this.selectedCalculation} inputsTypeValuePairs={this.inputsTypeValuePairs}
                                    productAttributes={this.productAttributes} orderCreated={this.orderCreated} orderSent={this.orderSent} countryStore={this.props.countryStore}
                                    customer={this.client} filteredCalculations={this.filteredCalculations} product={this.product} gnLanguage={this.gnLanguage} summaryMessageBoxData={this.summaryMessageBoxData}
                                    onClickSubmit={() => this.apiCall(this.step)} isEditMode={this.isEditMode}
                                    onMessageBarDismiss={(type: string) => { this.summaryMessageBoxData[type].hide = true; this.controlledForceUpdate(); } }
                                    inputsIdUserFieldsPairs={this.inputsIdUserFieldsPairs} asyncActionInProgress={this.state.asyncActionInProgress} policyFinalized={this.policyFinalized}
                                    goToInsuranceCompany={() => this.goToInsuranceCompany()} downloadPolicyDocuments={() => this.downloadPolicyDocuments()}
                                    getMapNameByProduct={(product: any) => this.getMapNameByProduct(product)} payloadType={this.props.policyCalculation && this.props.policyCalculation?.payloadType ? this.props.policyCalculation?.payloadType : ""}
                                    policyDocumentsDownloaded={this.state && this.state.additionalData && this.state.additionalData.policyDocumentsDownloaded ? this.state.additionalData.policyDocumentsDownloaded : false}
                                    insurancePolicyResponse={this.insurancePolicyResponse} inspectionLinkSent={this.inspectionLinkSent}
                                    onUwAcceptanceCodeChange={(value: string) => { this.uwAcceptanceCode = value; this.controlledForceUpdate(); }}
                                    uwAcceptanceCode={this.uwAcceptanceCode} selectedOfferAgreements={this.selectedOfferAgreements} summaryAgreements={this.summaryAgreements}
                                    toggleSummaryAgreement={(agreement: AgreementDto) => { this.summaryAgreements[agreement.code] = agreement; this.controlledForceUpdate(); }}
                                    toggleAllSummaryAgreements={(agreements: AgreementDto[]) => { this.summaryAgreements = agreements; this.controlledForceUpdate(); }}
                                    generateOcTermination={(formData: any) => this.generateOcTermination(formData)} 
                                    ocTerminationGenerated={this.state && this.state.additionalData && this.state.additionalData.ocTerminationGenerated ? this.state.additionalData.ocTerminationGenerated : false}
                                    sendPolicyMail={() => this.sendPolicyMail()} 
                                    policyMailSended={this.state && this.state.additionalData && this.state.additionalData.policyMailSended ? this.state.additionalData.policyMailSended : false}
                                /> : <></>}
                        </PivotItem>
                    </Pivot>
                }
            </LayerHost>
        </>;
    }
}