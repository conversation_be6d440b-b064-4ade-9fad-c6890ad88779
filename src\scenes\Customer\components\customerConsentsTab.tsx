import React from "react";
import { DefaultButton, mergeStyleSets, MessageBar, MessageBarType, PrimaryButton, ProgressIndicator, SelectionMode, ShimmeredDetailsList, Spinner, SpinnerSize, Stack, Text } from "@fluentui/react";
import { L } from "../../../lib/abpUtility";
import { myTheme } from "../../../styles/theme";
import { CheckBoxBase } from "../../BaseComponents/CheckBoxBase";
import clientAttachedFilesService from "../../../services/attachedFiles/clientAttachedFilesService";
import { ClientDto } from "../../../services/client/dto/clientDto";
import { catchErrorMessage, dateFormat } from "../../../utils/utils";
import { uploadFileToAzure } from "../../../services/azureService";
import { SelectedConsentCheckboxes } from "./customerContentView";

const classNames = mergeStyleSets({
    smallLoadSpinner: {
        display: 'inline-flex',
        marginLeft: '10px !important',
        marginTop: '22px',
        selectors: {
            '& .ms-Spinner-label': {
                color: myTheme.palette.themePrimary,
            }
        }
    },
    loadSpinner: {
        display: 'inline-flex',
        marginLeft: '45px !important',
        marginTop: '20px',
        selectors: {
            '& .ms-Spinner-label': {
                color: myTheme.palette.themePrimary,
            }
        }
    },
    messageBar: {
        width: 'fit-content',
        marginLeft: '25px !important',
        marginTop: '20px',
        selectors: {
            '& .ms-MessageBar-innerText': {
                selectors: {
                    '& span': {
                        whiteSpace: 'pre-line',
                    }
                }
            }
        }
    },
    uploadButton: {
        width: 'fit-content',
        padding: '5px 10px',
        marginTop: '20px',
    },
    attachedFilesLabel: {
        fontWeight: 'bold',
        marginTop: '20px',
        marginBottom: '20px',
    },
    stackSpacer: {
        marginBottom: '15px',
        paddingBottom: '40px',
        borderBottom: '1px solid lightgray'
    }
});

export interface ICustomerConsentsTabProps {
    isDataLoaded: boolean;
    asyncActionInProgress: boolean;
    customer: ClientDto;
    selectedConsentCheckboxes: SelectedConsentCheckboxes;
    clientHaveAgreements: {accepted: boolean, sentToClient: boolean};
    agreementsSentToClient: boolean;
    agreementsSentToClientError: string;
    agreementsSentToClientMessageBarType: MessageBarType;
    clientAttachedFiles: JSX.Element[];
    mobileAgreements: JSX.Element[];
    onMessageBarDismiss: () => void;
    setAsyncActionInProgress: (value: boolean) => void;
    setAgreementsSentToClient: (value: boolean) => void;
    setAgreementsSentToClientError: (value: string, messageBarType?: MessageBarType) => void;
    setSelectedConsentCheckboxes: (selectedConsentCheckboxes: SelectedConsentCheckboxes) => void;
    setClientAttachedFiles: (value: any) => void;
    setCustomerToPayloadModel: () => void;
    refreshCustomerData: () => void;
}

export class CustomerConsentsTab extends React.Component<ICustomerConsentsTabProps> {
    private fileUploadInputRef: any;
    private selectedFileForUpload: {name: string, src: string} = {
        name: "",
        src: "",
    };
    private consentHistory: any[] = [];
    private smsCode: string = "";
    private smsConsent: boolean = false;
    private resendSmsEnable: boolean = false;
    private scanConsent: boolean = false;
    private smsTimer: number = 0;
    private smsIntervalHandle: any = null;

    constructor(props: any) {
        super(props);
        this.fileUploadInputRef = React.createRef();
    }

    componentDidMount(): void {
        this.refreshConsentHistory();
    }

    private triggerUpload = () => {
        this.fileUploadInputRef.current.click();
    };
    
    private async onUpload() {
        const {customer, setAsyncActionInProgress} = this.props;

        setAsyncActionInProgress(true);

        this.props.setCustomerToPayloadModel();
        const selectedFile = !!this.fileUploadInputRef.current.files && this.fileUploadInputRef.current.files.length ? this.fileUploadInputRef.current.files[0] : null;
        
        if(!!selectedFile) {
            this.selectedFileForUpload.name = selectedFile.name;
            this.selectedFileForUpload.src = URL.createObjectURL(selectedFile);
        }

        this.forceUpdate();

        let result = await uploadFileToAzure(selectedFile, true);

        await clientAttachedFilesService.uploadScan(customer.id, result.url).then(async (response: any) => {
            if(response) {
                this.props.setAgreementsSentToClientError(L('Scan uploaded successfully.'), MessageBarType.success);
                this.props.refreshCustomerData();
            }

            setAsyncActionInProgress(false);
            await this.refreshConsentHistory();
        }).catch((error: any) => {
            console.error(error);
            this.props.setAgreementsSentToClientError(catchErrorMessage(error));

            setAsyncActionInProgress(false);
        });
    }

    private async sendSmsConsent() {
        const {customer, selectedConsentCheckboxes, setAsyncActionInProgress} = this.props;
        
        if (!customer?.id) return;

        setAsyncActionInProgress(true);
        
        await clientAttachedFilesService.sendClientAgreements(
            customer.id,
            selectedConsentCheckboxes['mandatoryAgreement'],
            selectedConsentCheckboxes['commercialAgreement'],
            true
        ).then(async (result: any) => {
            if(result === true) {
                await clientAttachedFilesService.sendSmsCode(customer.id);
                this.startSmsTimer();
                this.resendSmsEnable = true;

                this.props.setAgreementsSentToClient(true);
                this.props.refreshCustomerData();
                await this.refreshConsentHistory();
            } else {
                this.props.setAgreementsSentToClientError(L('Something went wrong. Try again later or contact with administrator.'));
            }
        }).catch((error: any) => {
            console.error(error);
            this.props.setAgreementsSentToClientError(catchErrorMessage(error));
        });
    
        setAsyncActionInProgress(false);
    }

    private async resendSms() {
        const {customer, setAsyncActionInProgress} = this.props;

        if (!customer?.id) return;

        setAsyncActionInProgress(true);
    
        await clientAttachedFilesService.sendSmsCode(customer.id);
        this.startSmsTimer();

        setAsyncActionInProgress(false);

        await this.refreshConsentHistory();
    }

    private startSmsTimer() {
        this.smsTimer = 60;
        this.forceUpdate();
    
        this.smsIntervalHandle = setInterval(() => {
            this.smsTimer -= 1;
            if (this.smsTimer <= 0) {
                clearInterval(this.smsIntervalHandle);
                this.smsIntervalHandle = null;
            }
            this.forceUpdate();
        }, 1000);
    }

    private async checkSmsCode() {
        const {customer, setAsyncActionInProgress} = this.props;

        if (!customer?.id || !this.smsCode) return;

        setAsyncActionInProgress(true);
    
        const result = await clientAttachedFilesService.checkSmsCode(customer.id, this.smsCode);
        if (result !== true) {
            this.props.setAgreementsSentToClientError(L("Invalid code"));
        } else {
            this.smsCode = "";
            this.props.refreshCustomerData();
        }

        setAsyncActionInProgress(false);

        await this.refreshConsentHistory();
    }

    private async sendScanConsent() {
        const {customer, selectedConsentCheckboxes, setAsyncActionInProgress} = this.props;

        if (!customer?.id) return;

        setAsyncActionInProgress(true);
        
        await clientAttachedFilesService.sendClientAgreements(
            customer.id,
            selectedConsentCheckboxes['mandatoryAgreement'],
            selectedConsentCheckboxes['commercialAgreement'],
            false
        ).then(async (result: any) => {
            if(result === true) {
                this.props.setAgreementsSentToClient(true);
                this.props.setAgreementsSentToClientError(L("Consents sent."), MessageBarType.success);
                
                this.props.refreshCustomerData();
                await this.refreshConsentHistory();
            } else {
                this.props.setAgreementsSentToClientError(L('Something went wrong. Try again later or contact with administrator.'));
            }
        }).catch((error: any) => {
            console.error(error);
            this.props.setAgreementsSentToClientError(catchErrorMessage(error));
        });

        setAsyncActionInProgress(false);
    }

    private async cancelAgreement() {
        const {customer, setAsyncActionInProgress} = this.props;

        if (!customer?.id) {
            this.props.setCustomerToPayloadModel();
        };

        const comment = prompt(L("Enter cancellation reason"));
        if (!comment) return;

        setAsyncActionInProgress(true);
    
        await clientAttachedFilesService.cancelAgreement(customer.id, comment).then((result: any) => {
            if(result === true) {
                this.props.setAgreementsSentToClient(false);
                this.props.setAgreementsSentToClientError(L("Agreement cancelled."), MessageBarType.success);
                this.resendSmsEnable = false;
                this.smsCode = "";
                this.smsTimer = 0;
                this.props.refreshCustomerData();
            } else {
                this.props.setAgreementsSentToClientError(L('Something went wrong. Try again later or contact with administrator.'));
            }
        }).catch((error: any) => {
            console.error(error);
            this.props.setAgreementsSentToClientError(catchErrorMessage(error));
        });

        setAsyncActionInProgress(false);

        await this.refreshConsentHistory();
    }

    private async refreshConsentHistory() {
        const {setAsyncActionInProgress} = this.props;
        setAsyncActionInProgress(true);

        const history = await clientAttachedFilesService.getClientAgreementsHistory(this.props.customer.id);
        this.consentHistory = Array.isArray(history.items) ? history.items : [];
        
        setAsyncActionInProgress(false);
        
        this.forceUpdate();
    }

    private async pdfFileClick() {
        try {
            const link = await clientAttachedFilesService.getPdfLink(this.props.customer.id);
            if (link) {
                const response = await fetch(link);
                const blob = await response.blob();
                const url = window.URL.createObjectURL(blob);
                const fullName = this.props.customer.user?.fullName?.replace(/\s+/g, '-') || 'document';
                const fileName = `${fullName}_rodo.pdf`;
                const a = document.createElement('a');
                a.href = url;
                a.download = fileName;
                document.body.appendChild(a);
                a.click();
                document.body.removeChild(a);

                window.URL.revokeObjectURL(url);
            } else {
                console.warn('PDF link not found');
            }
        } catch (error) {
            console.error('Failed to fetch PDF link:', error);
        }
    }

    render() {
        const {selectedConsentCheckboxes, agreementsSentToClientError, asyncActionInProgress, isDataLoaded, clientAttachedFiles, clientHaveAgreements, setSelectedConsentCheckboxes} = this.props;

        return <>
            <Stack horizontal horizontalAlign="start" className={classNames.stackSpacer}>
                <Stack style={{maxWidth: '42%', marginRight: '50px'}}>
                    <h3 style={{fontWeight: 'bold'}}>{L("Mandatory consents")}:</h3>
                    <CheckBoxBase label={L("contentOfTheClientsMandatoryConsent")} 
                        value={selectedConsentCheckboxes['mandatoryAgreement']} disabled={!isDataLoaded || clientHaveAgreements.accepted}
                        containerCustomStyles={{marginTop: '5px'}}
                        onChange={(e: any) => {
                            let copyOfSelectedConsentCheckboxes = {...selectedConsentCheckboxes};
                            copyOfSelectedConsentCheckboxes['mandatoryAgreement'] = e;
                            setSelectedConsentCheckboxes(copyOfSelectedConsentCheckboxes);
                        }}
                    />
                </Stack>

                <Stack style={{maxWidth: '42%'}}>
                    <h3 style={{fontWeight: 'bold'}}>{L("Commercial consents")}:</h3>
                    <CheckBoxBase label={L("contentOfTheClientsCommercialConsent")} 
                        value={selectedConsentCheckboxes['commercialAgreement']} disabled={!isDataLoaded || clientHaveAgreements.accepted}
                        containerCustomStyles={{marginTop: '5px'}}
                        onChange={(e: any) => {
                            let copyOfSelectedConsentCheckboxes = {...selectedConsentCheckboxes};
                            copyOfSelectedConsentCheckboxes['commercialAgreement'] = e;
                            setSelectedConsentCheckboxes(copyOfSelectedConsentCheckboxes);
                        }}
                    />
                </Stack>
            </Stack>

            <Stack className={classNames.stackSpacer} style={{paddingBottom: '20px'}}>
                <Text variant="large" className={classNames.attachedFilesLabel}>
                    { L('Acceptance of consents') }
                </Text>

                <Stack horizontal verticalAlign="center" tokens={{ childrenGap: 15 }} styles={{ root: { marginBottom: 20 } }}>
                    <CheckBoxBase
                        containerCustomStyles={{marginTop: 0}}
                        label={L("SMS")}
                        value={this.smsConsent}
                        disabled={asyncActionInProgress || clientHaveAgreements.accepted}
                        onChange={(e: any) => {
                            this.smsConsent = e;
                            if (e) this.scanConsent = false;
                            this.forceUpdate();
                        }}
                    />

                    {this.resendSmsEnable && this.smsTimer === 0 && !clientHaveAgreements.accepted && (
                        <PrimaryButton
                            text={L('Resend sms')}
                            disabled={asyncActionInProgress}
                            onClick={() => this.resendSms()}
                        />
                    )}

                    {this.smsTimer > 0 && !clientHaveAgreements.accepted && (
                        <div style={{ width: 200 }}>
                            <ProgressIndicator
                                label={`${L("Resend in")} ${this.smsTimer}s`}
                                percentComplete={(60 - this.smsTimer) / 60}
                            />
                        </div>
                    )}

                    <input
                        type="text"
                        placeholder={L("Enter SMS code")}
                        style={{ width: 120, padding: '6px' }}
                        value={this.smsCode}
                        disabled={!this.smsConsent || !clientHaveAgreements.sentToClient || clientHaveAgreements.accepted}
                        onChange={(e) => { this.smsCode = e.target.value; this.forceUpdate(); }}
                    />

                    <PrimaryButton
                        text={L("Check SMS")}
                        onClick={() => this.checkSmsCode()}
                        disabled={!this.smsConsent || !clientHaveAgreements.sentToClient || clientHaveAgreements.accepted}
                    />
                </Stack>

                <Stack horizontal verticalAlign="center" tokens={{ childrenGap: 15 }} styles={{ root: { marginBottom: 20 } }}>
                    <CheckBoxBase
                        containerCustomStyles={{marginTop: 0}}
                        label={L("Scan")}
                        value={this.scanConsent}
                        disabled={asyncActionInProgress || clientHaveAgreements.accepted}
                        onChange={(e: any) => {
                            this.scanConsent = e;
                            if (e) this.smsConsent = false; // disable SMS
                            this.forceUpdate();
                        }}
                    />

                    <PrimaryButton
                        text={L("Upload scan")}
                        onClick={this.triggerUpload}
                        disabled={!this.scanConsent || !clientHaveAgreements.sentToClient || asyncActionInProgress === true || clientHaveAgreements.accepted}
                        type={'file'}
                        iconProps={{ iconName: 'Upload' }}
                    />
                </Stack>

                <input ref={this.fileUploadInputRef} type="file" accept="application/pdf" style={{display: 'none'}} onChange={() => this.onUpload()} />

                {!!agreementsSentToClientError &&
                    <MessageBar messageBarType={this.props.agreementsSentToClientMessageBarType} isMultiline={false} className={`${classNames.messageBar}`}
                        onDismiss={() => { this.props.onMessageBarDismiss(); }}
                    >
                        {agreementsSentToClientError}
                    </MessageBar>
                }

                {asyncActionInProgress && (
                    <Spinner label={L('Please wait...')} className={classNames.loadSpinner} size={SpinnerSize.large} ariaLive="assertive" labelPosition="top" />
                )}
            </Stack>

            <Stack className={classNames.stackSpacer} style={{paddingBottom: '20px'}}>
                <Stack horizontal tokens={{ childrenGap: 25 }}>
                    <PrimaryButton 
                        className={classNames.uploadButton}
                        theme={myTheme}
                        text={L('Send consents to client')}
                        type={'button'}
                        onClick={() => this.smsConsent === true ? this.sendSmsConsent() : this.sendScanConsent()}
                        disabled={asyncActionInProgress === true || clientHaveAgreements.accepted || !selectedConsentCheckboxes['mandatoryAgreement'] ||
                            (!this.scanConsent && (!this.smsConsent || this.smsTimer > 0))
                        }
                        iconProps={{ iconName: 'MailForward' }}
                        style={{marginBottom: 25}}
                    />

                    <DefaultButton text={L("Reject customer consent")} theme={myTheme} type={'button'}
                        disabled={asyncActionInProgress || !clientHaveAgreements.accepted} className={classNames.uploadButton}
                        style={asyncActionInProgress || !clientHaveAgreements.accepted ? {} :
                            {border: `1px solid ${myTheme.palette.red}`, color: myTheme.palette.red}}
                        onClick={() => this.cancelAgreement()} 
                    />
                </Stack>

                <Text variant="large" className={classNames.attachedFilesLabel}>
                    { L('Attached files:') }
                </Text>

                <ul style={{padding: 0, listStyle: 'none'}}>
                {
                    clientAttachedFiles && clientAttachedFiles.length > 0 ?
                    clientAttachedFiles.map((element: any, index: number) => (
                                <li key={index} style={{ cursor: 'pointer' }} onClick={(e) => {
                                        e.preventDefault();
                                        this.pdfFileClick();
                                    }}
                                >
                                    {element.element}
                                </li>
                    ))
                    : 
                    L('There are no files to display')
                }
                </ul>
            </Stack>

            <Stack>
                <Text variant="large" className={classNames.attachedFilesLabel}>
                    { L('Consent history') }
                </Text>

                <div>
                    <ShimmeredDetailsList
                        columns={
                            [          
                                {
                                    key: 'status',
                                    name: L('Status'),
                                    minWidth: 100,
                                    maxWidth: 200,
                                    fieldName: 'status',
                                    onRender: (item: any): any => {
                                        return L(item.status);
                                    }
                                },
                                {
                                    key: 'date',
                                    name: L('Date'),
                                    minWidth: 100,
                                    maxWidth: 150,
                                    fieldName: 'date',
                                    onRender: (item: any): any => {
                                        return dateFormat(item.date, undefined, true);
                                    }
                                },
                                {
                                    key: 'agent',
                                    name: L('Agent'),
                                    minWidth: 100,
                                    maxWidth: 200,
                                    fieldName: 'agent',
                                    onRender: (item: any): any => {
                                        return item && item.agent ? item.agent.fullName : L('Client');
                                    }
                                },
                                {
                                    key: 'description',
                                    name: L('Note'),
                                    minWidth: 100,
                                    maxWidth: 300,
                                    isResizable: true,
                                    fieldName: 'description',
                                },
                            ]
                        }
                        items={this.consentHistory}
                        selectionMode={SelectionMode.none}
                        enableShimmer={asyncActionInProgress}
                    />
                </div>
            </Stack>
        </>;
    }
}